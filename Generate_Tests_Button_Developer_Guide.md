# Generate Tests Button - Developer Quick Guide

## 🎯 What Was Added

A **Generate Tests** button that automatically fetches test data from external Calgenie API and creates worksheets in LIMS.

---

## 🔧 Code Changes Made

### 1. **UI Changes** 
**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`

```html
<!-- NEW: Conditional button that appears only when CaligenConfigFlg = 1 -->
@if (int.Parse(GlobalVariables.GetGlobalVariables("CaligenConfigFlg")) == 1)
{
    <div class="control-column col-sm-2" Id="TstGntbtn">
        <button type="button" id="generate-tests-btn" class="caliber-button-primary" 
                value="@Ui.TestGenCap" onclick="TestGenBtnClick()">
            @Ui.TestGenCap
        </button>
    </div>
}
```

### 2. **JavaScript Function**
**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`

```javascript
// NEW: Function that calls external API and refreshes test list
function TestGenBtnClick(){
    if ($("#SpecID").val() != '' && $("#ProductMatId").val() != '') {
        $.ajax({
            method: "GET",
            url: serviceBase + "Test/ReadDataFromCalgenie?prodcode=" + $("#ProdCode").val() + 
                 "&prodNme=" + $("#ProdName").val(),
            cache: false,
            complete: function(data){
                // Refresh the test list after API call
                $.ajax({
                    method: "GET",
                    url: serviceBase + "Test/TestsListDisplay?PlantId=" + @Model.PlantId + 
                         "&ProdId=" + $("#ProductMatId").val() + "&SpecId=" + $("#SpecID").val(),
                    cache: false,
                    success: function (data) {
                        if (data.length > 0) {
                            $("#TestsListDisplay").html(data);
                            $("#TestsListDisplay").removeClass("d-none");
                        }
                    }
                });
            }
        });
    }    
}
```

### 3. **Backend Controller Method**
**File**: `Src\CaliberLIMS.UI\Pages\Test\TestController.cs`

```csharp
// NEW: Method that fetches data from Calgenie API and pushes to internal API
[HttpGet]
[Route("~/Test/ReadDataFromCalgenie")]
public async Task<IActionResult> ReadDataFromCalgenie(string prodcode, string prodNme)
{
    var httpClient = _httpClientFactory.CreateClient();          
    try
    {
        // 1. Call external Calgenie API
        string apiUrl = _configuration["CaligenApi"];
        string externalApiUrl = $"{apiUrl}?productName={prodNme}&productCode={prodcode}";
        var response = await httpClient.GetAsync(externalApiUrl);

        if (!response.IsSuccessStatusCode)
            return BadRequest("Failed to get data from external API.");

        var jsonString = await response.Content.ReadAsStringAsync();
        var testDetailsList = JsonConvert.DeserializeObject<List<FileTestDto>>(jsonString);

        // 2. Push each test to internal API for processing
        int DocCnt = 1;
        foreach (var testDetails in testDetailsList)
        {
            var pushRequest = new PushTestRequest
            {
                DocumentNo = testDetails.DocumentNo + "_" + DocCnt,
                CreatedOn = testDetails.PublishedDate,
                TestJson = testDetails.TestJson
            };

            string pushUrl = ListPageCaptions.ApiUrlPart + "PushTestDetails";
            var content = new StringContent(JsonConvert.SerializeObject(pushRequest), 
                                          Encoding.UTF8, "application/json");

            await new CommonHttpClientHandlerIHttpClientFactory(_httpClientFactory)
                  .PostAsync(pushUrl, content);
            DocCnt++;
        }

        return Ok("Data inserted successfully.");
    }
    catch (Exception ex)
    {
        return Ok("Error");
    }
}
```

---

## 🗄️ Database Changes

### **New Table**: `LMS_BAL_TST_DET_INF_WST`

```sql
-- Stores test data from external API before processing
CREATE TABLE LMS_BAL_TST_DET_INF_WST (
    LMS_BAL_TST_DET_INF_ID INT IDENTITY(1,1) PRIMARY KEY,
    DocumentNo NVARCHAR(255),           -- Document number from external system
    CreatedOn DATETIME,                 -- When record was created
    TestJson NTEXT,                     -- JSON data with test details
    Status INT DEFAULT 0,               -- 0 = unprocessed, 1 = processed
    ISMOA INT DEFAULT 0,                -- 1 = Method of Analysis, 0 = regular
    PublishedDate NVARCHAR(50),         -- When test was published externally
    JsonId NVARCHAR(255)                -- Unique identifier for JSON data
);
```

### **New Entity Model**
**File**: `Src\Models\CaliberLIMS.Models\WorkSheetModel.cs`

```csharp
// NEW: Entity for storing worksheet test details
public class PushWorkSheetDetails
{
    [Column("LMS_BAL_TST_DET_INF_ID")]
    public int Id { get; set; }
    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
    public int Status { get; set; }
    public int ISMOA { get; set; }
    public string PublishedDate { get; set; }
    public string JsonId { get; set; }
}

// NEW: DTO for API operations
public class PushWSDetailsDto
{
    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
    public int ISMOA { get; set; }
    public string PublishedDate { get; set; }
    public string JsonId { get; set; }
}
```

### **Database Context Update**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\ApplicationDbContext.cs`

```csharp
// NEW: DbSet for worksheet test details
public DbSet<PushWorkSheetDetails> LMS_BAL_TST_DET_INF_WST { get; set; }
```

---

## 🔄 Data Flow

```
1. User clicks "Generate Tests" button
   ↓
2. JavaScript calls ReadDataFromCalgenie endpoint
   ↓
3. Controller fetches data from external Calgenie API
   ↓
4. Data is pushed to WorksheetPushTestDetails API
   ↓
5. Data stored in LMS_BAL_TST_DET_INF_WST table (Status = 0)
   ↓
6. WorksheetJSONParsing processes unprocessed records
   ↓
7. Creates worksheets via TrnCldFinalSubmission
   ↓
8. Updates record status to 1 (processed)
   ↓
9. UI refreshes to show new tests
```

---

## ⚙️ Configuration Required

### **1. Global Variable**
```
CaligenConfigFlg = 1  (to show the button)
```

### **2. API Configuration**
```json
{
  "CaligenApi": "https://your-calgenie-api-endpoint.com/api/tests"
}
```

### **3. UI Caption** (needs implementation)
**File**: `Src\CaliberLIMS.UI\Pages\Test\UI.cs`
```csharp
public static string TestGenCap => CaptionCulture.GetCaption("A0XXX");
```

---

## 🧪 Testing Checklist

### **Frontend Testing**
- [ ] Button appears when `CaligenConfigFlg = 1`
- [ ] Button hidden when `CaligenConfigFlg = 0`
- [ ] Button disabled when Product/Spec not selected
- [ ] Test list refreshes after button click

### **Backend Testing**
- [ ] `ReadDataFromCalgenie` calls external API correctly
- [ ] Data is stored in `LMS_BAL_TST_DET_INF_WST` table
- [ ] `WorksheetJSONParsing` processes stored data
- [ ] Worksheets are created successfully

### **Database Testing**
- [ ] Records inserted with Status = 0
- [ ] Duplicate detection works (DocumentNo + JsonId)
- [ ] Status updated to 1 after processing
- [ ] JSON data stored correctly

---

## 🚨 Important Notes

1. **Button Visibility**: Only shows when `CaligenConfigFlg = 1`
2. **Validation**: Requires Product/Material and Specification selection
3. **Error Handling**: Continues processing even if individual records fail
4. **Duplicate Prevention**: Checks DocumentNo + JsonId before inserting
5. **Status Management**: 0 = unprocessed, 1 = processed

---

## 🔍 Key Files Modified

| File | Purpose | Change Type |
|------|---------|-------------|
| `CreationList.cshtml` | UI Page | Added button + JavaScript |
| `TestController.cs` | UI Controller | Added ReadDataFromCalgenie method |
| `WorkSheetModel.cs` | Models | Added PushWorkSheetDetails + DTO |
| `ApplicationDbContext.cs` | Database Context | Added DbSet |
| `WorkSheetController.cs` | API Controller | Enhanced JSON parsing |

---

## 🎯 Quick Start

1. **Set Configuration**: `CaligenConfigFlg = 1`
2. **Configure API**: Set `CaligenApi` endpoint
3. **Run Migrations**: Ensure `LMS_BAL_TST_DET_INF_WST` table exists
4. **Test**: Select Product/Spec and click "Generate Tests"
5. **Verify**: Check test list updates and database records

---

**✅ Ready to Use!** The Generate Tests button is now fully functional and integrated with the LIMS system.
