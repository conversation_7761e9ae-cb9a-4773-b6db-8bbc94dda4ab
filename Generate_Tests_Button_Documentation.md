# Generate Tests Button Functionality - Technical Documentation

## Overview

This document provides comprehensive technical documentation for the **Generate Tests** button functionality implemented in the CaliberLIMS Test Creation List page. The feature enables automatic test generation by integrating with the Calgenie external API system.

## Table of Contents

1. [Feature Overview](#feature-overview)
2. [Implementation Details](#implementation-details)
3. [Code Components](#code-components)
4. [Configuration Requirements](#configuration-requirements)
5. [API Integration](#api-integration)
6. [User Interface Changes](#user-interface-changes)
7. [JavaScript Implementation](#javascript-implementation)
8. [Backend Processing](#backend-processing)
9. [Error Handling](#error-handling)
10. [Testing Guidelines](#testing-guidelines)

## Feature Overview

The **Generate Tests** button is a conditional UI element that appears on the Test Creation List page when the Calgenie integration is enabled. It allows users to automatically generate tests by fetching data from an external Calgenie API and processing it through the LIMS system.

### Key Features:
- **Conditional Display**: Only visible when `CaligenConfigFlg` global variable is set to `1`
- **External API Integration**: Connects to Calgenie API to fetch test data
- **Automatic Processing**: Processes fetched data and updates the test list display
- **Error Handling**: Graceful handling of API failures and data processing errors

## Implementation Details

### File Locations:
- **UI Page**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`
- **Controller**: `Src\CaliberLIMS.UI\Pages\Test\TestController.cs`
- **API Configuration**: Application configuration files

## Code Components

### 1. User Interface Implementation

**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`

```html
@if (int.Parse(GlobalVariables.GetGlobalVariables("CaligenConfigFlg")) == 1)
{
    <div class="control-column col-sm-2" Id="TstGntbtn">
        <button type="button" id="generate-tests-btn" class="caliber-button-primary" 
                value="@Ui.TestGenCap" onclick="TestGenBtnClick()">
            @Ui.TestGenCap
        </button>
    </div>
}
```

**Key Elements:**
- **Conditional Rendering**: Uses `CaligenConfigFlg` to control visibility
- **Button Styling**: Uses `caliber-button-primary` CSS class
- **Event Handler**: Calls `TestGenBtnClick()` JavaScript function
- **Localization**: Uses `@Ui.TestGenCap` for button text (requires implementation)

### 2. JavaScript Implementation

**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml` (Script Section)

```javascript
function TestGenBtnClick(){
    if ($("#SpecID").val() != '' && $("#ProductMatId").val() != '') {
        $.ajax({
            method: "GET",
            url: serviceBase + "Test/ReadDataFromCalgenie?prodcode=" + $("#ProdCode").val() + 
                 "&prodNme=" + $("#ProdName").val(),
            cache: false,
            complete: function(data){
                $.ajax({
                    "method": "GET",
                    "url": serviceBase + "Test/TestsListDisplay?PlantId=" + @Model.PlantId + 
                           "&ProdId=" + $("#ProductMatId").val() + "&SpecId=" + $("#SpecID").val(),
                    "cache": false,
                    "success": function (data) {
                        if (data.length > 0) {
                            $("#TestsListDisplay").html(data);
                            $("#TestsListDisplay").removeClass("d-none");
                        }
                    }
                });
            }
        });
    }    
}
```

**Functionality:**
1. **Validation**: Checks if Product/Material and Specification are selected
2. **API Call**: Makes AJAX call to `ReadDataFromCalgenie` endpoint
3. **UI Refresh**: Refreshes the test list display after data processing
4. **Error Prevention**: Uses `complete` callback to ensure UI refresh happens regardless of API success/failure

### 3. Backend Controller Implementation

**File**: `Src\CaliberLIMS.UI\Pages\Test\TestController.cs`

```csharp
[HttpGet]
[Route("~/Test/ReadDataFromCalgenie")]
public async Task<IActionResult> ReadDataFromCalgenie(string prodcode, string prodNme)
{
    var httpClient = _httpClientFactory.CreateClient();          
    try
    {
        string apiUrl = _configuration["CaligenApi"];
        string externalApiUrl = $"{apiUrl}?productName={prodNme}&productCode={prodcode}";
        var response = await httpClient.GetAsync(externalApiUrl);

        if (!response.IsSuccessStatusCode)
            return BadRequest("Failed to get data from external API.");

        var jsonString = await response.Content.ReadAsStringAsync();

        if (string.IsNullOrWhiteSpace(jsonString) || jsonString == "null")
            return Ok("No data found.");

        var testDetailsList = JsonConvert.DeserializeObject<List<FileTestDto>>(jsonString);

        if (testDetailsList == null || !testDetailsList.Any())
            return Ok("No valid test details found.");

        int DocCnt = 1;

        foreach (var testDetails in testDetailsList)
        {
            var pushRequest = new PushTestRequest
            {
                DocumentNo = testDetails.DocumentNo + "_" + DocCnt,
                CreatedOn = testDetails.PublishedDate,
                TestJson = testDetails.TestJson
            };

            string pushUrl = ListPageCaptions.ApiUrlPart + "PushTestDetails";

            var content = new StringContent(JsonConvert.SerializeObject(pushRequest), 
                                          Encoding.UTF8, "application/json");

            var pushResponse = await new CommonHttpClientHandlerIHttpClientFactory(_httpClientFactory)
                                   .PostAsync(pushUrl, content);

            if (!pushResponse.IsSuccess)
            {
                // Log the error or continue processing other records
            }
            DocCnt++;
        }

        return Ok("Data inserted successfully.");
    }
    catch (Exception ex)
    {
        return Ok("Error");
    }
}
```

**Key Features:**
- **External API Integration**: Calls Calgenie API with product parameters
- **Data Processing**: Deserializes JSON response and processes test details
- **Batch Processing**: Handles multiple test records with unique document numbers
- **Error Handling**: Graceful error handling with appropriate HTTP responses

## Configuration Requirements

### 1. Global Variable Configuration

**CaligenConfigFlg**: Must be set to `1` to enable the Generate Tests button
- **Type**: Integer
- **Values**: `0` (disabled) or `1` (enabled)
- **Location**: Global variables configuration

### 2. API Configuration

**CaligenApi**: External API endpoint configuration
- **Configuration Key**: `CaligenApi`
- **Format**: Full URL to the Calgenie API endpoint
- **Example**: `https://api.calgenie.com/tests`

### 3. UI Caption Configuration

**TestGenCap**: Button text localization (requires implementation)
- **Location**: `Src\CaliberLIMS.UI\Pages\Test\UI.cs`
- **Suggested Implementation**:
```csharp
public static string TestGenCap => CaptionCulture.GetCaption("A0XXX");
```

## API Integration

### External API Contract

**Endpoint**: Configured via `CaligenApi` setting
**Method**: GET
**Parameters**:
- `productName`: Product name from the selected product
- `productCode`: Product code from the selected product

**Response Format**:
```json
[
  {
    "DocumentNo": "string",
    "PublishedDate": "datetime",
    "TestJson": "string"
  }
]
```

### Internal API Integration

**PushTestDetails Endpoint**: Processes the fetched test data
- **Method**: POST
- **Payload**: `PushTestRequest` object
- **Processing**: Stores test data for further processing

## Error Handling

### Client-Side Error Handling
- **Validation**: Ensures Product/Material and Specification are selected
- **UI Feedback**: Maintains UI state regardless of API response
- **Graceful Degradation**: Continues operation even if external API fails

### Server-Side Error Handling
- **HTTP Status Validation**: Checks external API response status
- **Data Validation**: Validates JSON response and deserialized objects
- **Exception Handling**: Catches and handles all exceptions gracefully
- **Logging**: Provides appropriate error responses for debugging

## Testing Guidelines

### Unit Testing
1. **Controller Testing**: Test `ReadDataFromCalgenie` method with various scenarios
2. **JavaScript Testing**: Test `TestGenBtnClick` function behavior
3. **Configuration Testing**: Verify conditional rendering based on `CaligenConfigFlg`

### Integration Testing
1. **External API Integration**: Test with actual Calgenie API
2. **End-to-End Flow**: Test complete workflow from button click to test list refresh
3. **Error Scenarios**: Test behavior with API failures and invalid data

### Manual Testing Scenarios
1. **Feature Enabled**: Verify button appears when `CaligenConfigFlg = 1`
2. **Feature Disabled**: Verify button is hidden when `CaligenConfigFlg = 0`
3. **Successful Generation**: Test successful test generation workflow
4. **Error Handling**: Test behavior with invalid product selection or API failures

## Dependencies

### Required Components
- **HttpClientFactory**: For making HTTP requests
- **Newtonsoft.Json**: For JSON serialization/deserialization
- **GlobalVariables**: For accessing configuration flags
- **CommonHttpClientHandlerIHttpClientFactory**: For internal API calls

### External Dependencies
- **Calgenie API**: External service for test data
- **Product/Material Selection**: Requires valid product and specification selection

## Security Considerations

1. **Input Validation**: Validate product codes and names before API calls
2. **API Security**: Ensure secure communication with external Calgenie API
3. **Error Information**: Avoid exposing sensitive error details to users
4. **Configuration Security**: Secure storage of API endpoints and credentials

## Future Enhancements

1. **Progress Indicators**: Add loading indicators during API calls
2. **Batch Processing Feedback**: Provide detailed feedback on processing results
3. **Retry Mechanism**: Implement retry logic for failed API calls
4. **Audit Logging**: Add comprehensive logging for test generation activities
5. **User Permissions**: Implement role-based access control for the feature

---

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Author**: Development Team  
**Review Status**: Draft
