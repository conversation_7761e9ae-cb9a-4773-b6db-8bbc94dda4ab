# Generate Tests Button Functionality - Technical Documentation

## Overview

This document provides comprehensive technical documentation for the **Generate Tests** button functionality implemented in the CaliberLIMS Test Creation List page. The feature enables automatic test generation by integrating with the Calgenie external API system.

## Table of Contents

1. [Feature Overview](#feature-overview)
2. [Implementation Details](#implementation-details)
3. [Code Components](#code-components)
4. [Database Components](#database-components)
5. [Configuration Requirements](#configuration-requirements)
6. [API Integration](#api-integration)
7. [User Interface Changes](#user-interface-changes)
8. [JavaScript Implementation](#javascript-implementation)
9. [Backend Processing](#backend-processing)
10. [Error Handling](#error-handling)
11. [Testing Guidelines](#testing-guidelines)

## Feature Overview

The **Generate Tests** button is a conditional UI element that appears on the Test Creation List page when the Calgenie integration is enabled. It allows users to automatically generate tests by fetching data from an external Calgenie API and processing it through the LIMS system.

### Key Features:
- **Conditional Display**: Only visible when `CaligenConfigFlg` global variable is set to `1`
- **External API Integration**: Connects to Calgenie API to fetch test data
- **Automatic Processing**: Processes fetched data and updates the test list display
- **Error Handling**: Graceful handling of API failures and data processing errors

## Implementation Details

### File Locations:
- **UI Page**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`
- **Controller**: `Src\CaliberLIMS.UI\Pages\Test\TestController.cs`
- **API Configuration**: Application configuration files

## Code Components

### 1. User Interface Implementation

**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`

```html
@if (int.Parse(GlobalVariables.GetGlobalVariables("CaligenConfigFlg")) == 1)
{
    <div class="control-column col-sm-2" Id="TstGntbtn">
        <button type="button" id="generate-tests-btn" class="caliber-button-primary" 
                value="@Ui.TestGenCap" onclick="TestGenBtnClick()">
            @Ui.TestGenCap
        </button>
    </div>
}
```

**Key Elements:**
- **Conditional Rendering**: Uses `CaligenConfigFlg` to control visibility
- **Button Styling**: Uses `caliber-button-primary` CSS class
- **Event Handler**: Calls `TestGenBtnClick()` JavaScript function
- **Localization**: Uses `@Ui.TestGenCap` for button text (requires implementation)

### 2. JavaScript Implementation

**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml` (Script Section)

```javascript
function TestGenBtnClick(){
    if ($("#SpecID").val() != '' && $("#ProductMatId").val() != '') {
        $.ajax({
            method: "GET",
            url: serviceBase + "Test/ReadDataFromCalgenie?prodcode=" + $("#ProdCode").val() + 
                 "&prodNme=" + $("#ProdName").val(),
            cache: false,
            complete: function(data){
                $.ajax({
                    "method": "GET",
                    "url": serviceBase + "Test/TestsListDisplay?PlantId=" + @Model.PlantId + 
                           "&ProdId=" + $("#ProductMatId").val() + "&SpecId=" + $("#SpecID").val(),
                    "cache": false,
                    "success": function (data) {
                        if (data.length > 0) {
                            $("#TestsListDisplay").html(data);
                            $("#TestsListDisplay").removeClass("d-none");
                        }
                    }
                });
            }
        });
    }    
}
```

**Functionality:**
1. **Validation**: Checks if Product/Material and Specification are selected
2. **API Call**: Makes AJAX call to `ReadDataFromCalgenie` endpoint
3. **UI Refresh**: Refreshes the test list display after data processing
4. **Error Prevention**: Uses `complete` callback to ensure UI refresh happens regardless of API success/failure

### 3. Backend Controller Implementation

**File**: `Src\CaliberLIMS.UI\Pages\Test\TestController.cs`

```csharp
[HttpGet]
[Route("~/Test/ReadDataFromCalgenie")]
public async Task<IActionResult> ReadDataFromCalgenie(string prodcode, string prodNme)
{
    var httpClient = _httpClientFactory.CreateClient();          
    try
    {
        string apiUrl = _configuration["CaligenApi"];
        string externalApiUrl = $"{apiUrl}?productName={prodNme}&productCode={prodcode}";
        var response = await httpClient.GetAsync(externalApiUrl);

        if (!response.IsSuccessStatusCode)
            return BadRequest("Failed to get data from external API.");

        var jsonString = await response.Content.ReadAsStringAsync();

        if (string.IsNullOrWhiteSpace(jsonString) || jsonString == "null")
            return Ok("No data found.");

        var testDetailsList = JsonConvert.DeserializeObject<List<FileTestDto>>(jsonString);

        if (testDetailsList == null || !testDetailsList.Any())
            return Ok("No valid test details found.");

        int DocCnt = 1;

        foreach (var testDetails in testDetailsList)
        {
            var pushRequest = new PushTestRequest
            {
                DocumentNo = testDetails.DocumentNo + "_" + DocCnt,
                CreatedOn = testDetails.PublishedDate,
                TestJson = testDetails.TestJson
            };

            string pushUrl = ListPageCaptions.ApiUrlPart + "PushTestDetails";

            var content = new StringContent(JsonConvert.SerializeObject(pushRequest), 
                                          Encoding.UTF8, "application/json");

            var pushResponse = await new CommonHttpClientHandlerIHttpClientFactory(_httpClientFactory)
                                   .PostAsync(pushUrl, content);

            if (!pushResponse.IsSuccess)
            {
                // Log the error or continue processing other records
            }
            DocCnt++;
        }

        return Ok("Data inserted successfully.");
    }
    catch (Exception ex)
    {
        return Ok("Error");
    }
}
```

**Key Features:**
- **External API Integration**: Calls Calgenie API with product parameters
- **Data Processing**: Deserializes JSON response and processes test details
- **Batch Processing**: Handles multiple test records with unique document numbers
- **Error Handling**: Graceful error handling with appropriate HTTP responses

## Database Components

### 1. Database Tables

#### LMS_BAL_TST_DET_INF_WST Table
**Purpose**: Stores worksheet test details from external sources for processing

**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\ApplicationDbContext.cs`

<augment_code_snippet path="Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\ApplicationDbContext.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Represents the table in the database for storing push worksheet details.
/// Maps to the LMS_BAL_TST_DET_INF_WST table in the database.
/// </summary>
public DbSet<PushWorkSheetDetails> LMS_BAL_TST_DET_INF_WST { get; set; }
````
</augment_code_snippet>

**Key Columns:**
- `LMS_BAL_TST_DET_INF_ID` (Primary Key): Unique identifier for each record
- `DocumentNo`: Document number from external system
- `CreatedOn`: Timestamp when record was created
- `TestJson`: JSON data containing test details
- `Status`: Processing status (0 = unprocessed, 1 = processed)
- `ISMOA`: Flag indicating if this is a Method of Analysis worksheet
- `PublishedDate`: Date when the test was published in external system
- `JsonId`: Unique identifier for the JSON data

### 2. Entity Models

#### PushWorkSheetDetails Model
**File**: `Src\Models\CaliberLIMS.Models\WorkSheetModel.cs`

<augment_code_snippet path="Src\Models\CaliberLIMS.Models\WorkSheetModel.cs" mode="EXCERPT">
````csharp
public class PushWorkSheetDetails
{
    // Primary key column for the push test details table
    [Column("LMS_BAL_TST_DET_INF_ID")]
    public int Id { get; set; }

    // Column for storing the document number
    public string DocumentNo { get; set; }

    // Column for storing the creation timestamp
    public DateTime CreatedOn { get; set; }

    // Column for storing the JSON test data
    public string TestJson { get; set; }

    // Column for processing status
    public int Status { get; set; }

    // Column for MOA flag
    public int ISMOA { get; set; }

    // Column for published date
    public string PublishedDate { get; set; }

    // Column for JSON identifier
    public string JsonId { get; set; }
}
````
</augment_code_snippet>

#### PushWSDetailsDto Model
**File**: `Src\Models\CaliberLIMS.Models\WorkSheetModel.cs`

<augment_code_snippet path="Src\Models\CaliberLIMS.Models\WorkSheetModel.cs" mode="EXCERPT">
````csharp
public class PushWSDetailsDto
{
    // Column for storing the document number associated with the test details
    public string DocumentNo { get; set; }

    // Column for storing the date and time when the test details were created
    public DateTime CreatedOn { get; set; }

    // Column for storing the JSON test data
    public string TestJson { get; set; }

    // Column for MOA flag
    public int ISMOA { get; set; }

    // Column for published date
    public string PublishedDate { get; set; }

    // Column for JSON identifier
    public string JsonId { get; set; }
}
````
</augment_code_snippet>

### 3. Database Operations

#### WorksheetPushTestDetails API Endpoint
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

<augment_code_snippet path="Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs" mode="EXCERPT">
````csharp
[HttpPost]
[Route("WorksheetPushTestDetails")]
public async Task<IActionResult> WorksheetPushTestDetails([FromBody] PushWSDetailsDto PushWSDetailsDto)
{
    // Validate input data
    if (PushWSDetailsDto == null || string.IsNullOrEmpty(PushWSDetailsDto.TestJson))
    {
        return BadRequest("Invalid request data.");
    }

    try
    {
        // Check for existing records with same DocumentNo and JsonId
        var latestRecord = await _dbContext.LMS_BAL_TST_DET_INF_WST
               .Where(x => x.DocumentNo == PushWSDetailsDto.DocumentNo
                  && x.JsonId == PushWSDetailsDto.JsonId)
               .OrderByDescending(x => x.PublishedDate)
               .FirstOrDefaultAsync();

        // Only insert if no record exists or if this is a newer version
        if (latestRecord == null || PushWSDetailsDto.PublishedDate > latestRecord.PublishedDate)
        {
            PushWorkSheetDetails newpushWorkSheetDetails = new PushWorkSheetDetails
            {
                DocumentNo = PushWSDetailsDto.DocumentNo,
                CreatedOn = PushWSDetailsDto.CreatedOn,
                TestJson = PushWSDetailsDto.TestJson,
                Status = 0, // Unprocessed
                ISMOA = PushWSDetailsDto.ISMOA,
                PublishedDate = PushWSDetailsDto.PublishedDate,
                JsonId = PushWSDetailsDto.JsonId
            };

            await _dbContext.LMS_BAL_TST_DET_INF_WST.AddAsync(newpushWorkSheetDetails);
            await _dbContext.SaveChangesAsync();
        }
    }
    catch (Exception ex)
    {
        return StatusCode(500, "An error occurred while processing the request.");
    }

    return Ok("Test details pushed successfully.");
}
````
</augment_code_snippet>

#### WorksheetJSONParsing API Endpoint
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

<augment_code_snippet path="Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs" mode="EXCERPT">
````csharp
[HttpGet]
[Route("WorksheetJSONParsing")]
public async Task<IActionResult> WorksheetJSONParsing(int plantId = 1)
{
    try
    {
        // Check if there are unprocessed records
        if (!await _dbContext.LMS_BAL_TST_DET_INF_WST.AnyAsync(ptd => ptd.Status == 0))
        {
            return NotFound("No New records found or all records are already parsed.");
        }

        // Retrieve all unprocessed records
        List<PushWorkSheetDetails> pushWorkSheetDetails = await _dbContext.LMS_BAL_TST_DET_INF_WST
                .Where(ptd => ptd.Status == 0 && ptd.TestJson != null)
                .ToListAsync();

        foreach (var pushWorkSheetDetail in pushWorkSheetDetails)
        {
            // Process JSON data and create worksheets
            // ... JSON parsing logic ...

            // Call TrnCldFinalSubmission to create worksheet
            var status = await TrnCldFinalSubmission(plantId, 1, 0, 0, 0, 1, 1, 1,
                                                   Guid.NewGuid().ToString(), WorksheetModel);

            // Update status to processed
            pushWorkSheetDetail.Status = 1;
        }

        await _dbContext.SaveChangesAsync();
        return Ok("Worksheet has been parsed and Successfully Submitted");
    }
    catch (Exception ex)
    {
        return StatusCode(500, "An error occurred while parsing worksheet JSON.");
    }
}
````
</augment_code_snippet>

### 4. Stored Procedures

#### TrnCldFinalSubmission
**Purpose**: Processes and finalizes worksheet submission to the LIMS system

**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

<augment_code_snippet path="Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs" mode="EXCERPT">
````csharp
[HttpPost]
[Route("TrnCldFinalSubmission")]
public async Task<IActionResult> TrnCldFinalSubmission(int plantId, int IsSaved, int baseId,
    int aulId, int transId, int transType, int userId, int configId, string transCode,
    [FromBody] WorkSheetModel workSheetModel)
{
    // Initialize WorkSheetService with plant-specific information
    using var workSheetService = new WorkSheetService(PlanInfoList.GetPlanInfo(plantId).PlantCode)
    {
        IsSaved = IsSaved,
        BaseId = baseId,
        AulId = aulId,
        TransId = transId,
        TransType = transType,
        UserId = userId,
        ConfigId = configId,
        TransCode = transCode
    };

    // Process worksheet data through service layer
    var result = await workSheetService.ProcessWorksheet(workSheetModel);
    return Ok(result);
}
````
</augment_code_snippet>

### 5. Data Flow

1. **Data Ingestion**: External Calgenie API data is received via `ReadDataFromCalgenie`
2. **Data Storage**: Test data is stored in `LMS_BAL_TST_DET_INF_WST` table via `WorksheetPushTestDetails`
3. **Data Processing**: `WorksheetJSONParsing` processes unprocessed records (Status = 0)
4. **Worksheet Creation**: `TrnCldFinalSubmission` creates actual worksheets in the LIMS system
5. **Status Update**: Processed records are marked with Status = 1

### 6. Database Schema Changes Required

#### New Table Creation (if not exists)
```sql
CREATE TABLE LMS_BAL_TST_DET_INF_WST (
    LMS_BAL_TST_DET_INF_ID INT IDENTITY(1,1) PRIMARY KEY,
    DocumentNo NVARCHAR(255),
    CreatedOn DATETIME,
    TestJson NTEXT,
    Status INT DEFAULT 0,
    ISMOA INT DEFAULT 0,
    PublishedDate NVARCHAR(50),
    JsonId NVARCHAR(255)
);
```

#### Index Recommendations
```sql
-- Index for status-based queries
CREATE INDEX IX_LMS_BAL_TST_DET_INF_WST_Status ON LMS_BAL_TST_DET_INF_WST (Status);

-- Index for document and JSON ID lookups
CREATE INDEX IX_LMS_BAL_TST_DET_INF_WST_DocJson ON LMS_BAL_TST_DET_INF_WST (DocumentNo, JsonId);

-- Index for published date ordering
CREATE INDEX IX_LMS_BAL_TST_DET_INF_WST_PublishedDate ON LMS_BAL_TST_DET_INF_WST (PublishedDate);
```

## Configuration Requirements

### 1. Global Variable Configuration

**CaligenConfigFlg**: Must be set to `1` to enable the Generate Tests button
- **Type**: Integer
- **Values**: `0` (disabled) or `1` (enabled)
- **Location**: Global variables configuration

### 2. API Configuration

**CaligenApi**: External API endpoint configuration
- **Configuration Key**: `CaligenApi`
- **Format**: Full URL to the Calgenie API endpoint
- **Example**: `https://api.calgenie.com/tests`

### 3. UI Caption Configuration

**TestGenCap**: Button text localization (requires implementation)
- **Location**: `Src\CaliberLIMS.UI\Pages\Test\UI.cs`
- **Suggested Implementation**:
```csharp
public static string TestGenCap => CaptionCulture.GetCaption("A0XXX");
```

## API Integration

### External API Contract

**Endpoint**: Configured via `CaligenApi` setting
**Method**: GET
**Parameters**:
- `productName`: Product name from the selected product
- `productCode`: Product code from the selected product

**Response Format**:
```json
[
  {
    "DocumentNo": "string",
    "PublishedDate": "datetime",
    "TestJson": "string"
  }
]
```

### Internal API Integration

#### WorksheetPushTestDetails Endpoint
**Purpose**: Receives and stores test data from external Calgenie API

- **Method**: POST
- **Route**: `/WorksheetPushTestDetails`
- **Payload**: `PushWSDetailsDto` object
- **Processing**:
  - Validates input data
  - Checks for duplicate records based on DocumentNo and JsonId
  - Stores new or updated test data in `LMS_BAL_TST_DET_INF_WST` table
  - Sets initial status to 0 (unprocessed)

#### WorksheetJSONParsing Endpoint
**Purpose**: Processes stored test data and creates worksheets

- **Method**: GET
- **Route**: `/WorksheetJSONParsing`
- **Parameters**: `plantId` (optional, defaults to 1)
- **Processing**:
  - Retrieves unprocessed records (Status = 0) from database
  - Parses JSON data to extract test details
  - Supports both regular and WSTypes JSON formats
  - Creates individual worksheets for each test/section
  - Calls `TrnCldFinalSubmission` to finalize worksheet creation
  - Updates record status to 1 (processed)

#### TrnCldFinalSubmission Endpoint
**Purpose**: Finalizes worksheet submission to LIMS system

- **Method**: POST
- **Route**: `/TrnCldFinalSubmission`
- **Parameters**: Plant ID, user context, and WorkSheetModel
- **Processing**:
  - Initializes WorkSheetService with plant-specific configuration
  - Processes worksheet data through service layer
  - Creates final worksheet records in LIMS database
  - Returns processing status and results

## Error Handling

### Client-Side Error Handling
- **Validation**: Ensures Product/Material and Specification are selected
- **UI Feedback**: Maintains UI state regardless of API response
- **Graceful Degradation**: Continues operation even if external API fails

### Server-Side Error Handling
- **HTTP Status Validation**: Checks external API response status
- **Data Validation**: Validates JSON response and deserialized objects
- **Exception Handling**: Catches and handles all exceptions gracefully
- **Logging**: Provides appropriate error responses for debugging

## Testing Guidelines

### Unit Testing
1. **Controller Testing**: Test `ReadDataFromCalgenie` method with various scenarios
2. **JavaScript Testing**: Test `TestGenBtnClick` function behavior
3. **Configuration Testing**: Verify conditional rendering based on `CaligenConfigFlg`

### Integration Testing
1. **External API Integration**: Test with actual Calgenie API
2. **Database Integration**: Test data storage and retrieval from LMS_BAL_TST_DET_INF_WST
3. **End-to-End Flow**: Test complete workflow from button click to worksheet creation
4. **JSON Processing**: Test both old and new JSON format processing
5. **Error Scenarios**: Test behavior with API failures and invalid data

### Database Testing
1. **Data Insertion**: Verify correct storage of test data in LMS_BAL_TST_DET_INF_WST
2. **Duplicate Handling**: Test duplicate detection based on DocumentNo and JsonId
3. **Status Updates**: Verify status changes from 0 (unprocessed) to 1 (processed)
4. **JSON Parsing**: Test parsing of different JSON structures
5. **Worksheet Creation**: Verify final worksheet creation in LIMS system

### Manual Testing Scenarios
1. **Feature Enabled**: Verify button appears when `CaligenConfigFlg = 1`
2. **Feature Disabled**: Verify button is hidden when `CaligenConfigFlg = 0`
3. **Successful Generation**: Test successful test generation workflow
4. **Error Handling**: Test behavior with invalid product selection or API failures

## Dependencies

### Required Components
- **HttpClientFactory**: For making HTTP requests
- **Newtonsoft.Json**: For JSON serialization/deserialization
- **GlobalVariables**: For accessing configuration flags
- **CommonHttpClientHandlerIHttpClientFactory**: For internal API calls
- **Entity Framework Core**: For database operations
- **ApplicationDbContext**: For accessing LMS_BAL_TST_DET_INF_WST table
- **WorkSheetService**: For processing worksheet data

### External Dependencies
- **Calgenie API**: External service for test data
- **Product/Material Selection**: Requires valid product and specification selection
- **SQL Server Database**: For storing test data and worksheet information
- **LIMS Database**: Target system for final worksheet creation

### Database Dependencies
- **LMS_BAL_TST_DET_INF_WST Table**: Primary storage for incoming test data
- **Worksheet Tables**: Target tables for processed worksheet data
- **Plant Configuration**: Plant-specific database connections and settings

## Security Considerations

1. **Input Validation**: Validate product codes and names before API calls
2. **API Security**: Ensure secure communication with external Calgenie API
3. **Error Information**: Avoid exposing sensitive error details to users
4. **Configuration Security**: Secure storage of API endpoints and credentials
5. **Database Security**: Implement proper access controls for LMS_BAL_TST_DET_INF_WST table
6. **JSON Validation**: Validate JSON structure before processing to prevent injection attacks
7. **Data Sanitization**: Sanitize input data before database storage

## Future Enhancements

1. **Progress Indicators**: Add loading indicators during API calls
2. **Batch Processing Feedback**: Provide detailed feedback on processing results
3. **Retry Mechanism**: Implement retry logic for failed API calls
4. **Audit Logging**: Add comprehensive logging for test generation activities
5. **User Permissions**: Implement role-based access control for the feature
6. **Database Optimization**: Implement database indexing and query optimization
7. **Data Archiving**: Implement archiving strategy for processed records
8. **Real-time Processing**: Consider real-time processing instead of batch processing
9. **Error Recovery**: Implement automatic error recovery and reprocessing mechanisms
10. **Performance Monitoring**: Add performance monitoring for database operations

---

**Document Version**: 1.0  
**Last Updated**: [Current Date]  
**Author**: Development Team  
**Review Status**: Draft
