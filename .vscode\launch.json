{"version": "0.2.0", "configurations": [{"name": "API", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/Src/Services/CaliberLIMS.Api/CaliberLIMS.Api/bin/Debug/net8.0/CaliberLIMS.Api.dll", "cwd": "${workspaceFolder}/Src/Services/CaliberLIMS.Api/CaliberLIMS.Api", "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:7001;http://localhost:7000", "ASPNETCORE_VSCODE_ENVIRONMENT": "true"}, "stopAtEntry": false, "console": "internalConsole"}, {"name": "UI", "type": "coreclr", "request": "launch", "program": "${workspaceFolder}/Src/CaliberLIMS.UI/bin/Debug/net8.0/CaliberLIMS.UI.dll", "cwd": "${workspaceFolder}/Src/CaliberLIMS.UI", "env": {"ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_URLS": "https://localhost:5001;http://localhost:5000", "ASPNETCORE_VSCODE_ENVIRONMENT": "true", "ASPNETCORE_REFFEREEXCLUDEPATH": "CaliberLIMS.UI", "ASPNETCORE_SESSIONEXCLUDEPATH": "/CaliberLIMS.UI"}, "stopAtEntry": false, "console": "internalConsole", "preLaunchTask": "Open UI in Browser"}], "compounds": [{"name": "Start API and UI", "configurations": ["API", "UI"], "stopAll": true}]}