# Test Generation - Developer Guide

## 🎯 Overview
The **Generate Tests Button** allows users to fetch test data from external Calgenie API and store it in the LIMS system for processing.

---

## 🖥️ Application Changes

### 1. **UI Button Implementation**
**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`

```html
<!-- NEW: Conditional Generate Tests Button -->
@if (int.Parse(GlobalVariables.GetGlobalVariables("CaligenConfigFlg")) == 1)
{
    <div class="control-column col-sm-2" Id="TstGntbtn">
        <button type="button" id="generate-tests-btn" class="caliber-button-primary" 
                value="@Ui.TestGenCap" onclick="TestGenBtnClick()">
            @Ui.TestGenCap
        </button>
    </div>
}
```

**Key Features:**
- Only visible when `CaligenConfigFlg = 1`
- Uses `caliber-button-primary` styling
- Calls `TestGenBtnClick()` JavaScript function

### 2. **JavaScript Implementation**
**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`

```javascript
// NEW: Generate Tests Button Click Handler
function TestGenBtnClick(){
    // Validate required fields
    if ($("#SpecID").val() != '' && $("#ProductMatId").val() != '') {
        
        // Step 1: Call external API to fetch and store test data
        $.ajax({
            method: "GET",
            url: serviceBase + "Test/ReadDataFromCalgenie?prodcode=" + $("#ProdCode").val() + 
                 "&prodNme=" + $("#ProdName").val(),
            cache: false,
            complete: function(data){
                
                // Step 2: Refresh test list display
                $.ajax({
                    method: "GET",
                    url: serviceBase + "Test/TestsListDisplay?PlantId=" + @Model.PlantId + 
                         "&ProdId=" + $("#ProductMatId").val() + "&SpecId=" + $("#SpecID").val(),
                    cache: false,
                    success: function (data) {
                        if (data.length > 0) {
                            $("#TestsListDisplay").html(data);
                            $("#TestsListDisplay").removeClass("d-none");
                        }
                    }
                });
            }
        });
    }    
}
```

**Functionality:**
1. Validates Product/Material and Specification selection
2. Calls `ReadDataFromCalgenie` to fetch external data
3. Refreshes test list display after processing

### 3. **Backend Controller**
**File**: `Src\CaliberLIMS.UI\Pages\Test\TestController.cs`

```csharp
// NEW: External API Integration Method
[HttpGet]
[Route("~/Test/ReadDataFromCalgenie")]
public async Task<IActionResult> ReadDataFromCalgenie(string prodcode, string prodNme)
{
    var httpClient = _httpClientFactory.CreateClient();          
    try
    {
        // Step 1: Call External Calgenie API
        string apiUrl = _configuration["CaligenApi"];
        string externalApiUrl = $"{apiUrl}?productName={prodNme}&productCode={prodcode}";
        var response = await httpClient.GetAsync(externalApiUrl);

        if (!response.IsSuccessStatusCode)
            return BadRequest("Failed to get data from external API.");

        var jsonString = await response.Content.ReadAsStringAsync();

        if (string.IsNullOrWhiteSpace(jsonString) || jsonString == "null")
            return Ok("No data found.");

        // Step 2: Deserialize Response
        var testDetailsList = JsonConvert.DeserializeObject<List<FileTestDto>>(jsonString);

        if (testDetailsList == null || !testDetailsList.Any())
            return Ok("No valid test details found.");

        // Step 3: Push Data to Internal API
        int DocCnt = 1;
        foreach (var testDetails in testDetailsList)
        {
            var pushRequest = new PushTestRequest
            {
                DocumentNo = testDetails.DocumentNo + "_" + DocCnt,
                CreatedOn = testDetails.PublishedDate,
                TestJson = testDetails.TestJson
            };

            string pushUrl = ListPageCaptions.ApiUrlPart + "PushTestDetails";
            var content = new StringContent(JsonConvert.SerializeObject(pushRequest), 
                                          Encoding.UTF8, "application/json");

            var pushResponse = await new CommonHttpClientHandlerIHttpClientFactory(_httpClientFactory)
                                   .PostAsync(pushUrl, content);

            if (!pushResponse.IsSuccess)
            {
                // Log error but continue processing other records
            }
            DocCnt++;
        }

        return Ok("Data inserted successfully.");
    }
    catch (Exception ex)
    {
        return Ok("Error");
    }
}
```

**Key Features:**
- Fetches data from external Calgenie API
- Processes multiple test records with unique document numbers
- Pushes data to internal `PushTestDetails` endpoint
- Handles errors gracefully

---

## 🗄️ Database Changes

### 1. **Storage Table**
**Table**: `LMS_BAL_TST_DET_INF_WST`

```sql
-- NEW: Table for storing test data from external API
CREATE TABLE LMS_BAL_TST_DET_INF_WST (
    LMS_BAL_TST_DET_INF_ID INT IDENTITY(1,1) PRIMARY KEY,
    DocumentNo NVARCHAR(255),           -- Document number from external system
    CreatedOn DATETIME,                 -- When record was created
    TestJson NTEXT,                     -- Raw JSON data from external API
    Status INT DEFAULT 0,               -- Processing status (0=unprocessed, 1=processed)
    ISMOA INT DEFAULT 0,                -- Method of Analysis flag (0=regular, 1=MOA)
    PublishedDate NVARCHAR(50),         -- Publication date from external system
    JsonId NVARCHAR(255)                -- Unique identifier for JSON data
);

-- Recommended Indexes
CREATE INDEX IX_LMS_BAL_TST_DET_INF_WST_Status ON LMS_BAL_TST_DET_INF_WST (Status);
CREATE INDEX IX_LMS_BAL_TST_DET_INF_WST_DocJson ON LMS_BAL_TST_DET_INF_WST (DocumentNo, JsonId);
```

### 2. **Entity Model**
**File**: `Src\Models\CaliberLIMS.Models\WorkSheetModel.cs`

```csharp
// NEW: Entity for test data storage
public class PushWorkSheetDetails
{
    [Column("LMS_BAL_TST_DET_INF_ID")]
    public int Id { get; set; }
    
    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
    public int Status { get; set; }
    public int ISMOA { get; set; }
    public string PublishedDate { get; set; }
    public string JsonId { get; set; }
}

// NEW: DTO for API operations
public class PushWSDetailsDto
{
    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
    public int ISMOA { get; set; }
    public string PublishedDate { get; set; }
    public string JsonId { get; set; }
}
```

### 3. **Database Context**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\ApplicationDbContext.cs`

```csharp
// NEW: DbSet for test data storage
public DbSet<PushWorkSheetDetails> LMS_BAL_TST_DET_INF_WST { get; set; }
```

### 4. **API Endpoint for Data Storage**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

```csharp
// NEW: Endpoint to store test data from external API
[HttpPost]
[Route("WorksheetPushTestDetails")]
public async Task<IActionResult> WorksheetPushTestDetails([FromBody] PushWSDetailsDto PushWSDetailsDto)
{
    if (PushWSDetailsDto == null || string.IsNullOrEmpty(PushWSDetailsDto.TestJson))
    {
        return BadRequest("Invalid request data.");
    }
    
    try
    {
        // Check for existing records (duplicate prevention)
        var latestRecord = await _dbContext.LMS_BAL_TST_DET_INF_WST
               .Where(x => x.DocumentNo == PushWSDetailsDto.DocumentNo
                  && x.JsonId == PushWSDetailsDto.JsonId)
               .OrderByDescending(x => x.PublishedDate)
               .FirstOrDefaultAsync();
               
        // Only insert if no record exists or this is newer
        if (latestRecord == null || PushWSDetailsDto.PublishedDate > latestRecord.PublishedDate)
        {
            PushWorkSheetDetails newRecord = new PushWorkSheetDetails
            {
                DocumentNo = PushWSDetailsDto.DocumentNo,
                CreatedOn = PushWSDetailsDto.CreatedOn,
                TestJson = PushWSDetailsDto.TestJson,
                Status = 0, // Unprocessed
                ISMOA = PushWSDetailsDto.ISMOA,
                PublishedDate = PushWSDetailsDto.PublishedDate,
                JsonId = PushWSDetailsDto.JsonId
            };

            await _dbContext.LMS_BAL_TST_DET_INF_WST.AddAsync(newRecord);
            await _dbContext.SaveChangesAsync();
        }
        
        return Ok("Test details stored successfully.");
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error storing test details.");
    }
}
```

---

## ⚙️ Configuration

### 1. **Global Variable**
```
CaligenConfigFlg = 1  (enables the Generate Tests button)
```

### 2. **API Configuration**
```json
{
  "CaligenApi": "https://your-calgenie-api-endpoint.com/api/tests"
}
```

### 3. **UI Caption** (requires implementation)
**File**: `Src\CaliberLIMS.UI\Pages\Test\UI.cs`
```csharp
public static string TestGenCap => CaptionCulture.GetCaption("A0XXX");
```

---

## 🔄 Data Flow

```
1. User clicks "Generate Tests" button
   ↓
2. JavaScript validates Product/Specification selection
   ↓
3. AJAX call to ReadDataFromCalgenie endpoint
   ↓
4. Controller fetches data from external Calgenie API
   ↓
5. Data is pushed to WorksheetPushTestDetails API
   ↓
6. Test data stored in LMS_BAL_TST_DET_INF_WST table
   ↓
7. Status set to 0 (unprocessed, ready for worksheet generation)
   ↓
8. UI test list refreshes to show updated data
```

---

## 🧪 Testing

### **Frontend Testing**
- [ ] Button appears when `CaligenConfigFlg = 1`
- [ ] Button hidden when `CaligenConfigFlg = 0`
- [ ] Validation prevents action without Product/Spec selection
- [ ] Test list refreshes after successful generation

### **Backend Testing**
- [ ] External API call succeeds with valid product data
- [ ] Multiple test records processed correctly
- [ ] Error handling works for API failures
- [ ] Data pushed to internal API successfully

### **Database Testing**
- [ ] Records inserted into `LMS_BAL_TST_DET_INF_WST`
- [ ] Status set to 0 (unprocessed)
- [ ] Duplicate detection works (DocumentNo + JsonId)
- [ ] JSON data stored correctly

---

## 📋 Files Modified

| File | Purpose | Change |
|------|---------|--------|
| `CreationList.cshtml` | UI Page | Added Generate Tests button |
| `TestController.cs` | UI Controller | Added ReadDataFromCalgenie method |
| `WorkSheetModel.cs` | Models | Added PushWorkSheetDetails entities |
| `ApplicationDbContext.cs` | DB Context | Added DbSet for test storage |
| `WorkSheetController.cs` | API Controller | Added WorksheetPushTestDetails endpoint |

---

**✅ Test Generation Complete!** 
Data is now stored and ready for worksheet generation processing.
