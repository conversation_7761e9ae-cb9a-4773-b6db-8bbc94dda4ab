# Test Generation - Developer Guide

## 🎯 Overview
The **Generate Tests Button** allows users to fetch test data from external Calgenie API and store it in the LIMS system for processing.

---

## 🖥️ Application Changes

### 1. **UI Button Implementation**
**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`

```html
<!-- NEW: Conditional Generate Tests Button -->
@if (int.Parse(GlobalVariables.GetGlobalVariables("CaligenConfigFlg")) == 1)
{
    <div class="control-column col-sm-2" Id="TstGntbtn">
        <button type="button" id="generate-tests-btn" class="caliber-button-primary" 
                value="@Ui.TestGenCap" onclick="TestGenBtnClick()">
            @Ui.TestGenCap
        </button>
    </div>
}
```

**Key Features:**
- Only visible when `CaligenConfigFlg = 1`
- Uses `caliber-button-primary` styling
- Calls `TestGenBtnClick()` JavaScript function

### 2. **JavaScript Implementation**
**File**: `Src\CaliberLIMS.UI\Pages\Test\CreationList.cshtml`

```javascript
// NEW: Generate Tests Button Click Handler
function TestGenBtnClick(){
    // Validate required fields
    if ($("#SpecID").val() != '' && $("#ProductMatId").val() != '') {
        
        // Step 1: Call external API to fetch and store test data
        $.ajax({
            method: "GET",
            url: serviceBase + "Test/ReadDataFromCalgenie?prodcode=" + $("#ProdCode").val() + 
                 "&prodNme=" + $("#ProdName").val(),
            cache: false,
            complete: function(data){
                
                // Step 2: Refresh test list display
                $.ajax({
                    method: "GET",
                    url: serviceBase + "Test/TestsListDisplay?PlantId=" + @Model.PlantId + 
                         "&ProdId=" + $("#ProductMatId").val() + "&SpecId=" + $("#SpecID").val(),
                    cache: false,
                    success: function (data) {
                        if (data.length > 0) {
                            $("#TestsListDisplay").html(data);
                            $("#TestsListDisplay").removeClass("d-none");
                        }
                    }
                });
            }
        });
    }    
}
```

**Functionality:**
1. Validates Product/Material and Specification selection
2. Calls `ReadDataFromCalgenie` to fetch external data
3. Refreshes test list display after processing

### 3. **Backend Controller**
**File**: `Src\CaliberLIMS.UI\Pages\Test\TestController.cs`

```csharp
// NEW: External API Integration Method
[HttpGet]
[Route("~/Test/ReadDataFromCalgenie")]
public async Task<IActionResult> ReadDataFromCalgenie(string prodcode, string prodNme)
{
    var httpClient = _httpClientFactory.CreateClient();          
    try
    {
        // Step 1: Call External Calgenie API
        string apiUrl = _configuration["CaligenApi"];
        string externalApiUrl = $"{apiUrl}?productName={prodNme}&productCode={prodcode}";
        var response = await httpClient.GetAsync(externalApiUrl);

        if (!response.IsSuccessStatusCode)
            return BadRequest("Failed to get data from external API.");

        var jsonString = await response.Content.ReadAsStringAsync();

        if (string.IsNullOrWhiteSpace(jsonString) || jsonString == "null")
            return Ok("No data found.");

        // Step 2: Deserialize Response
        var testDetailsList = JsonConvert.DeserializeObject<List<FileTestDto>>(jsonString);

        if (testDetailsList == null || !testDetailsList.Any())
            return Ok("No valid test details found.");

        // Step 3: Push Data to Internal API
        int DocCnt = 1;
        foreach (var testDetails in testDetailsList)
        {
            var pushRequest = new PushTestRequest
            {
                DocumentNo = testDetails.DocumentNo + "_" + DocCnt,
                CreatedOn = testDetails.PublishedDate,
                TestJson = testDetails.TestJson
            };

            string pushUrl = ListPageCaptions.ApiUrlPart + "PushTestDetails";
            var content = new StringContent(JsonConvert.SerializeObject(pushRequest), 
                                          Encoding.UTF8, "application/json");

            var pushResponse = await new CommonHttpClientHandlerIHttpClientFactory(_httpClientFactory)
                                   .PostAsync(pushUrl, content);

            if (!pushResponse.IsSuccess)
            {
                // Log error but continue processing other records
            }
            DocCnt++;
        }

        return Ok("Data inserted successfully.");
    }
    catch (Exception ex)
    {
        return Ok("Error");
    }
}
```

**Key Features:**
- Fetches data from external Calgenie API
- Processes multiple test records with unique document numbers
- Pushes data to internal `PushTestDetails` endpoint
- Handles errors gracefully

---

## 🗄️ Database Changes

### 1. **Test Storage Table (MISSING - NEEDS TO BE ADDED)**
**Table**: `LMS_BAL_TST_DET_INF`

```sql
-- NEW: Table for storing test data from external API (MISSING)
CREATE TABLE LMS_BAL_TST_DET_INF (
    LMS_BAL_TST_DET_INF_ID INT IDENTITY(1,1) PRIMARY KEY,
    DocumentNo NVARCHAR(255),           -- Document number from external system
    CreatedOn DATETIME,                 -- When record was created
    TestJson NTEXT,                     -- Raw JSON data from external API
    Status INT DEFAULT 0,               -- Processing status (0=unprocessed, 1=processed)
    PublishedDate NVARCHAR(50),         -- Publication date from external system
    JsonId NVARCHAR(255)                -- Unique identifier for JSON data
);

-- Recommended Indexes
CREATE INDEX IX_LMS_BAL_TST_DET_INF_Status ON LMS_BAL_TST_DET_INF (Status);
CREATE INDEX IX_LMS_BAL_TST_DET_INF_DocNo ON LMS_BAL_TST_DET_INF (DocumentNo);
```

### 2. **Worksheet Storage Table (EXISTING)**
**Table**: `LMS_BAL_TST_DET_INF_WST`

```sql
-- EXISTING: Table for storing worksheet data from external API
CREATE TABLE LMS_BAL_TST_DET_INF_WST (
    LMS_BAL_TST_DET_INF_ID INT IDENTITY(1,1) PRIMARY KEY,
    DocumentNo NVARCHAR(255),           -- Document number from external system
    CreatedOn DATETIME,                 -- When record was created
    TestJson NTEXT,                     -- Raw JSON data from external API
    Status INT DEFAULT 0,               -- Processing status (0=unprocessed, 1=processed)
    ISMOA INT DEFAULT 0,                -- Method of Analysis flag (0=regular, 1=MOA)
    PublishedDate NVARCHAR(50),         -- Publication date from external system
    JsonId NVARCHAR(255)                -- Unique identifier for JSON data
);

-- Recommended Indexes
CREATE INDEX IX_LMS_BAL_TST_DET_INF_WST_Status ON LMS_BAL_TST_DET_INF_WST (Status);
CREATE INDEX IX_LMS_BAL_TST_DET_INF_WST_DocJson ON LMS_BAL_TST_DET_INF_WST (DocumentNo, JsonId);
```

### 3. **Entity Models (MISSING - NEED TO BE ADDED)**
**File**: `Src\Models\CaliberLIMS.Models\WorkSheetModel.cs`

```csharp
// MISSING: Entity for test data storage in LMS_BAL_TST_DET_INF table
public class PushTestDetails
{
    [Column("LMS_BAL_TST_DET_INF_ID")]
    public int Id { get; set; }

    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
    public int Status { get; set; }
    public string PublishedDate { get; set; }
    public string JsonId { get; set; }
}

// MISSING: DTO for test API operations
public class PushTestRequest
{
    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
}

// EXISTING: Entity for worksheet data storage in LMS_BAL_TST_DET_INF_WST table
public class PushWorkSheetDetails
{
    [Column("LMS_BAL_TST_DET_INF_ID")]
    public int Id { get; set; }

    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
    public int Status { get; set; }
    public int ISMOA { get; set; }
    public string PublishedDate { get; set; }
    public string JsonId { get; set; }
}

// EXISTING: DTO for worksheet API operations
public class PushWSDetailsDto
{
    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
    public int ISMOA { get; set; }
    public string PublishedDate { get; set; }
    public string JsonId { get; set; }
}
```

### 4. **Database Context (EXISTING + MISSING)**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\ApplicationDbContext.cs`

```csharp
// MISSING: DbSet for test data storage (NEEDS TO BE ADDED)
public DbSet<PushTestDetails> LMS_BAL_TST_DET_INF { get; set; }

// EXISTING: DbSet for worksheet data storage
public DbSet<PushWorkSheetDetails> LMS_BAL_TST_DET_INF_WST { get; set; }
```

### 5. **Missing API Endpoint for Test Data Storage (NEEDS TO BE ADDED)**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

```csharp
// MISSING: Endpoint to store test data from external API (NEEDS TO BE ADDED)
[HttpPost]
[Route("PushTestDetails")]
public async Task<IActionResult> PushTestDetails([FromBody] PushTestRequest pushTestRequest)
{
    if (pushTestRequest == null || string.IsNullOrEmpty(pushTestRequest.TestJson))
    {
        return BadRequest("Invalid request data.");
    }

    try
    {
        // Check for existing records (duplicate prevention)
        var latestRecord = await _dbContext.LMS_BAL_TST_DET_INF
               .Where(x => x.DocumentNo == pushTestRequest.DocumentNo)
               .OrderByDescending(x => x.CreatedOn)
               .FirstOrDefaultAsync();

        // Only insert if no record exists or this is newer
        if (latestRecord == null || pushTestRequest.CreatedOn > latestRecord.CreatedOn)
        {
            PushTestDetails newRecord = new PushTestDetails
            {
                DocumentNo = pushTestRequest.DocumentNo,
                CreatedOn = pushTestRequest.CreatedOn,
                TestJson = pushTestRequest.TestJson,
                Status = 0, // Unprocessed
                PublishedDate = pushTestRequest.CreatedOn.ToString("yyyy-MM-dd HH:mm:ss"),
                JsonId = Guid.NewGuid().ToString()
            };

            await _dbContext.LMS_BAL_TST_DET_INF.AddAsync(newRecord);
            await _dbContext.SaveChangesAsync();
        }

        return Ok("Test details stored successfully.");
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error storing test details.");
    }
}
```

### 6. **Existing API Endpoint for Worksheet Data Storage**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

```csharp
// EXISTING: Endpoint to store worksheet data from external API
[HttpPost]
[Route("WorksheetPushTestDetails")]
public async Task<IActionResult> WorksheetPushTestDetails([FromBody] PushWSDetailsDto PushWSDetailsDto)
{
    if (PushWSDetailsDto == null || string.IsNullOrEmpty(PushWSDetailsDto.TestJson))
    {
        return BadRequest("Invalid request data.");
    }
    
    try
    {
        // Check for existing records (duplicate prevention)
        var latestRecord = await _dbContext.LMS_BAL_TST_DET_INF_WST
               .Where(x => x.DocumentNo == PushWSDetailsDto.DocumentNo
                  && x.JsonId == PushWSDetailsDto.JsonId)
               .OrderByDescending(x => x.PublishedDate)
               .FirstOrDefaultAsync();
               
        // Only insert if no record exists or this is newer
        if (latestRecord == null || PushWSDetailsDto.PublishedDate > latestRecord.PublishedDate)
        {
            PushWorkSheetDetails newRecord = new PushWorkSheetDetails
            {
                DocumentNo = PushWSDetailsDto.DocumentNo,
                CreatedOn = PushWSDetailsDto.CreatedOn,
                TestJson = PushWSDetailsDto.TestJson,
                Status = 0, // Unprocessed
                ISMOA = PushWSDetailsDto.ISMOA,
                PublishedDate = PushWSDetailsDto.PublishedDate,
                JsonId = PushWSDetailsDto.JsonId
            };

            await _dbContext.LMS_BAL_TST_DET_INF_WST.AddAsync(newRecord);
            await _dbContext.SaveChangesAsync();
        }
        
        return Ok("Test details stored successfully.");
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error storing test details.");
    }
}
```

---

## ⚙️ Configuration

### 1. **Global Variable**
```
CaligenConfigFlg = 1  (enables the Generate Tests button)
```

### 2. **API Configuration**
```json
{
  "CaligenApi": "https://your-calgenie-api-endpoint.com/api/tests"
}
```

### 3. **UI Caption** (requires implementation)
**File**: `Src\CaliberLIMS.UI\Pages\Test\UI.cs`
```csharp
public static string TestGenCap => CaptionCulture.GetCaption("A0XXX");
```

---

## 🔄 Data Flow

```
1. User clicks "Generate Tests" button
   ↓
2. JavaScript validates Product/Specification selection
   ↓
3. AJAX call to ReadDataFromCalgenie endpoint
   ↓
4. Controller fetches data from external Calgenie API
   ↓
5. Data is pushed to WorksheetPushTestDetails API
   ↓
6. Test data stored in LMS_BAL_TST_DET_INF_WST table
   ↓
7. Status set to 0 (unprocessed, ready for worksheet generation)
   ↓
8. UI test list refreshes to show updated data
```

---

## 🔧 Dependencies

### **Required NuGet Packages**
```xml
<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
<PackageReference Include="Microsoft.EntityFrameworkCore" Version="8.0.0" />
<PackageReference Include="Microsoft.Extensions.Http" Version="8.0.0" />
```

### **Required Services**
```csharp
// Program.cs or Startup.cs
services.AddHttpClient();
services.AddDbContext<ApplicationDbContext>(options =>
    options.UseSqlServer(connectionString));
```

---

## 🛡️ Security Considerations

### **Input Validation**
```csharp
// Validate product codes and names
if (string.IsNullOrWhiteSpace(prodcode) || string.IsNullOrWhiteSpace(prodNme))
{
    return BadRequest("Product code and name are required.");
}

// Sanitize input parameters
prodcode = prodcode.Trim();
prodNme = prodNme.Trim();
```

### **API Security**
```csharp
// Secure HTTP client configuration
var httpClient = _httpClientFactory.CreateClient();
httpClient.Timeout = TimeSpan.FromSeconds(30);
httpClient.DefaultRequestHeaders.Add("User-Agent", "CaliberLIMS/1.0");
```

### **Database Security**
```sql
-- Grant minimal required permissions
GRANT SELECT, INSERT, UPDATE ON LMS_BAL_TST_DET_INF_WST TO [CaliberLIMS_User];
```

---

## 📊 Monitoring & Logging

### **Application Logging**
```csharp
// Add logging to ReadDataFromCalgenie method
private readonly ILogger<TestController> _logger;

public async Task<IActionResult> ReadDataFromCalgenie(string prodcode, string prodNme)
{
    _logger.LogInformation("Starting test generation for Product: {ProductCode}", prodcode);

    try
    {
        // ... existing code ...
        _logger.LogInformation("Successfully processed {Count} test records", testDetailsList.Count);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error generating tests for Product: {ProductCode}", prodcode);
    }
}
```

### **Performance Monitoring**
```csharp
// Add performance counters
using var activity = Activity.StartActivity("TestGeneration");
activity?.SetTag("product.code", prodcode);
activity?.SetTag("product.name", prodNme);
```

---

## 🔄 Error Handling

### **Frontend Error Handling**
```javascript
function TestGenBtnClick(){
    if ($("#SpecID").val() != '' && $("#ProductMatId").val() != '') {

        // Show loading indicator
        $("#generate-tests-btn").prop("disabled", true).text("Generating...");

        $.ajax({
            method: "GET",
            url: serviceBase + "Test/ReadDataFromCalgenie?prodcode=" + $("#ProdCode").val() +
                 "&prodNme=" + $("#ProdName").val(),
            cache: false,
            success: function(response) {
                // Show success message
                toastr.success("Tests generated successfully!");
            },
            error: function(xhr, status, error) {
                // Show error message
                toastr.error("Failed to generate tests: " + error);
            },
            complete: function(data){
                // Re-enable button
                $("#generate-tests-btn").prop("disabled", false).text("@Ui.TestGenCap");

                // Refresh test list
                $.ajax({
                    method: "GET",
                    url: serviceBase + "Test/TestsListDisplay?PlantId=" + @Model.PlantId +
                         "&ProdId=" + $("#ProductMatId").val() + "&SpecId=" + $("#SpecID").val(),
                    cache: false,
                    success: function (data) {
                        if (data.length > 0) {
                            $("#TestsListDisplay").html(data);
                            $("#TestsListDisplay").removeClass("d-none");
                        }
                    }
                });
            }
        });
    } else {
        // Show validation message
        toastr.warning("Please select Product/Material and Specification first.");
    }
}
```

### **Backend Error Handling**
```csharp
public async Task<IActionResult> ReadDataFromCalgenie(string prodcode, string prodNme)
{
    var httpClient = _httpClientFactory.CreateClient();

    try
    {
        // Input validation
        if (string.IsNullOrWhiteSpace(prodcode) || string.IsNullOrWhiteSpace(prodNme))
        {
            return BadRequest(new { error = "Product code and name are required" });
        }

        // API call with timeout
        string apiUrl = _configuration["CaligenApi"];
        if (string.IsNullOrWhiteSpace(apiUrl))
        {
            return StatusCode(500, new { error = "Calgenie API URL not configured" });
        }

        string externalApiUrl = $"{apiUrl}?productName={Uri.EscapeDataString(prodNme)}&productCode={Uri.EscapeDataString(prodcode)}";

        using var cts = new CancellationTokenSource(TimeSpan.FromSeconds(30));
        var response = await httpClient.GetAsync(externalApiUrl, cts.Token);

        if (!response.IsSuccessStatusCode)
        {
            return StatusCode((int)response.StatusCode,
                new { error = $"External API returned {response.StatusCode}: {response.ReasonPhrase}" });
        }

        var jsonString = await response.Content.ReadAsStringAsync();

        if (string.IsNullOrWhiteSpace(jsonString) || jsonString == "null")
        {
            return Ok(new { message = "No data found for the specified product", count = 0 });
        }

        var testDetailsList = JsonConvert.DeserializeObject<List<FileTestDto>>(jsonString);

        if (testDetailsList == null || !testDetailsList.Any())
        {
            return Ok(new { message = "No valid test details found", count = 0 });
        }

        // Process with error tracking
        int successCount = 0;
        int errorCount = 0;
        var errors = new List<string>();

        foreach (var testDetails in testDetailsList)
        {
            try
            {
                var pushRequest = new PushTestRequest
                {
                    DocumentNo = $"{testDetails.DocumentNo}_{successCount + errorCount + 1}",
                    CreatedOn = testDetails.PublishedDate,
                    TestJson = testDetails.TestJson
                };

                string pushUrl = ListPageCaptions.ApiUrlPart + "PushTestDetails";
                var content = new StringContent(JsonConvert.SerializeObject(pushRequest),
                                              Encoding.UTF8, "application/json");

                var pushResponse = await new CommonHttpClientHandlerIHttpClientFactory(_httpClientFactory)
                                       .PostAsync(pushUrl, content);

                if (pushResponse.IsSuccess)
                {
                    successCount++;
                }
                else
                {
                    errorCount++;
                    errors.Add($"Failed to push {testDetails.DocumentNo}: {pushResponse.ErrorMessage}");
                }
            }
            catch (Exception ex)
            {
                errorCount++;
                errors.Add($"Error processing {testDetails.DocumentNo}: {ex.Message}");
            }
        }

        return Ok(new
        {
            message = "Processing completed",
            successCount,
            errorCount,
            errors = errors.Take(5).ToList() // Limit error details
        });
    }
    catch (OperationCanceledException)
    {
        return StatusCode(408, new { error = "Request timeout - external API took too long to respond" });
    }
    catch (HttpRequestException ex)
    {
        return StatusCode(503, new { error = $"Network error: {ex.Message}" });
    }
    catch (JsonException ex)
    {
        return StatusCode(500, new { error = $"Invalid JSON response from external API: {ex.Message}" });
    }
    catch (Exception ex)
    {
        _logger?.LogError(ex, "Unexpected error in ReadDataFromCalgenie");
        return StatusCode(500, new { error = "An unexpected error occurred" });
    }
}
```

---

## 🚀 Deployment Considerations

### **Database Migration**
```sql
-- Create table if not exists
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='LMS_BAL_TST_DET_INF_WST' AND xtype='U')
BEGIN
    CREATE TABLE LMS_BAL_TST_DET_INF_WST (
        LMS_BAL_TST_DET_INF_ID INT IDENTITY(1,1) PRIMARY KEY,
        DocumentNo NVARCHAR(255),
        CreatedOn DATETIME,
        TestJson NTEXT,
        Status INT DEFAULT 0,
        ISMOA INT DEFAULT 0,
        PublishedDate NVARCHAR(50),
        JsonId NVARCHAR(255)
    );

    -- Create indexes
    CREATE INDEX IX_LMS_BAL_TST_DET_INF_WST_Status ON LMS_BAL_TST_DET_INF_WST (Status);
    CREATE INDEX IX_LMS_BAL_TST_DET_INF_WST_DocJson ON LMS_BAL_TST_DET_INF_WST (DocumentNo, JsonId);
END
```

### **Configuration Deployment**
```json
// appsettings.json
{
  "CaligenApi": "https://production-calgenie-api.com/api/tests",
  "ConnectionStrings": {
    "DefaultConnection": "Server=prod-server;Database=CaliberLIMS;..."
  },
  "Logging": {
    "LogLevel": {
      "CaliberLIMS.UI.Pages.Test.TestController": "Information"
    }
  }
}
```

### **Feature Flag Deployment**
```sql
-- Enable the feature in production
UPDATE GlobalVariables
SET VariableValue = '1'
WHERE VariableName = 'CaligenConfigFlg';
```

---

## 📊 Performance Optimization

### **Database Optimization**
```sql
-- Partition large tables by date
CREATE PARTITION FUNCTION PF_TestData (DATETIME)
AS RANGE RIGHT FOR VALUES ('2024-01-01', '2024-07-01', '2025-01-01');

-- Archive old processed records
CREATE PROCEDURE SP_ArchiveProcessedTestData
AS
BEGIN
    -- Move records older than 6 months to archive table
    INSERT INTO LMS_BAL_TST_DET_INF_WST_ARCHIVE
    SELECT * FROM LMS_BAL_TST_DET_INF_WST
    WHERE Status = 1 AND CreatedOn < DATEADD(MONTH, -6, GETDATE());

    DELETE FROM LMS_BAL_TST_DET_INF_WST
    WHERE Status = 1 AND CreatedOn < DATEADD(MONTH, -6, GETDATE());
END
```

### **API Performance**
```csharp
// Add response caching
[ResponseCache(Duration = 300, VaryByQueryKeys = new[] { "prodcode", "prodNme" })]
public async Task<IActionResult> ReadDataFromCalgenie(string prodcode, string prodNme)
{
    // Implementation
}

// Add compression
services.AddResponseCompression(options =>
{
    options.EnableForHttps = true;
    options.Providers.Add<GzipCompressionProvider>();
});
```

---

## 🔧 Maintenance & Support

### **Health Checks**
```csharp
// Add health check for external API
services.AddHealthChecks()
    .AddCheck<CalgenieApiHealthCheck>("calgenie-api")
    .AddDbContext<ApplicationDbContext>();

public class CalgenieApiHealthCheck : IHealthCheck
{
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            var apiUrl = _configuration["CaligenApi"];
            var response = await _httpClient.GetAsync($"{apiUrl}/health", cancellationToken);

            return response.IsSuccessStatusCode
                ? HealthCheckResult.Healthy("Calgenie API is responsive")
                : HealthCheckResult.Unhealthy($"Calgenie API returned {response.StatusCode}");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy($"Calgenie API health check failed: {ex.Message}");
        }
    }
}
```

### **Monitoring Queries**
```sql
-- Monitor test generation activity
SELECT
    CAST(CreatedOn AS DATE) as Date,
    COUNT(*) as RecordsCreated,
    SUM(CASE WHEN Status = 0 THEN 1 ELSE 0 END) as Pending,
    SUM(CASE WHEN Status = 1 THEN 1 ELSE 0 END) as Processed,
    SUM(CASE WHEN Status = -1 THEN 1 ELSE 0 END) as Errors
FROM LMS_BAL_TST_DET_INF_WST
WHERE CreatedOn >= DATEADD(DAY, -7, GETDATE())
GROUP BY CAST(CreatedOn AS DATE)
ORDER BY Date DESC;

-- Check for stuck records
SELECT COUNT(*) as StuckRecords
FROM LMS_BAL_TST_DET_INF_WST
WHERE Status = 0 AND CreatedOn < DATEADD(HOUR, -2, GETDATE());
```

---

## 📋 Files Modified

| File | Purpose | Change |
|------|---------|--------|
| `CreationList.cshtml` | UI Page | Added Generate Tests button + enhanced JavaScript |
| `TestController.cs` | UI Controller | Added ReadDataFromCalgenie method with error handling |
| `WorkSheetModel.cs` | Models | Added PushWorkSheetDetails entities |
| `ApplicationDbContext.cs` | DB Context | Added DbSet for test storage |
| `WorkSheetController.cs` | API Controller | Added WorksheetPushTestDetails endpoint |
| `appsettings.json` | Configuration | Added CaligenApi endpoint configuration |
| `Program.cs` | Startup | Added HttpClient and health check services |

---

## 📞 Support Information

### **Troubleshooting Common Issues**

**Issue**: Button not visible
- **Solution**: Check `CaligenConfigFlg = 1` in GlobalVariables

**Issue**: External API timeout
- **Solution**: Increase timeout in HttpClient configuration

**Issue**: Duplicate records
- **Solution**: Check DocumentNo + JsonId uniqueness logic

**Issue**: JSON parsing errors
- **Solution**: Validate JSON structure from external API

---

**✅ Test Generation Complete!**
Data is now stored and ready for worksheet generation processing.
