# Worksheet Generation - Developer Guide

## 🎯 Overview
The **Worksheet Generation** system processes stored test data and creates actual worksheets in the LIMS system, supporting both old and new JSON formats with WSTypes.

---

## 🖥️ Application Changes

### 1. **Enhanced JSON Processing Models**
**File**: `Src\Models\CaliberLIMS.Models\WorkSheetModel.cs`

```csharp
// NEW: Model for handling WSTypes JSON format
public class DraftTestDetailWithWSTypes
{
    [JsonProperty("TestName")]
    public string TestName { get; set; }
    
    [JsonProperty("WSTypes")]
    public List<WSType> WSTypes { get; set; }
    
    [JsonProperty("Images")]
    public List<object> Images { get; set; }
}

// NEW: Model for individual worksheet sections
public class WSType
{
    [JsonProperty("Type")]
    public string Type { get; set; }
    
    [JsonProperty("SecName")]
    public string SecName { get; set; }
    
    [JsonProperty("Procedure")]
    public string Procedure { get; set; }
}
```

### 2. **Enhanced JSON Parsing API**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

```csharp
// ENHANCED: WorksheetJSONParsing with dual format support
[HttpGet]
[Route("WorksheetJSONParsing")]
public async Task<IActionResult> WorksheetJSONParsing(int plantId = 1)
{
    try
    {
        // Check for unprocessed records
        if (!await _dbContext.LMS_BAL_TST_DET_INF_WST.AnyAsync(ptd => ptd.Status == 0))
        {
            return NotFound("No New records found or all records are already parsed.");
        }

        // Retrieve unprocessed records
        List<PushWorkSheetDetails> pushWorkSheetDetails = await _dbContext.LMS_BAL_TST_DET_INF_WST
                .Where(ptd => ptd.Status == 0 && ptd.TestJson != null)
                .ToListAsync();

        foreach (var pushWorkSheetDetail in pushWorkSheetDetails)
        {
            try
            {
                // NEW: Try parsing with WSTypes format first
                var testDetailsWithWSTypes = JsonConvert.DeserializeObject<DraftTestDetailWithWSTypes>(pushWorkSheetDetail.TestJson);
                
                if (testDetailsWithWSTypes?.WSTypes != null && testDetailsWithWSTypes.WSTypes.Any())
                {
                    // NEW FORMAT: Process each WSType as separate worksheet
                    foreach (var wsType in testDetailsWithWSTypes.WSTypes)
                    {
                        if (wsType.Type == "section")
                        {
                            var WorksheetModel = new WorkSheetModel
                            {
                                TestName = wsType.SecName,  // Use section name as test name
                                Procedure = wsType.Procedure,
                                DocumentNo = pushWorkSheetDetail.DocumentNo,
                                ISMOA = pushWorkSheetDetail.ISMOA == 1
                            };

                            // Create worksheet for this section
                            await TrnCldFinalSubmission(plantId, 1, 0, 0, 0, 1, 1, 1, 
                                                      Guid.NewGuid().ToString(), WorksheetModel);
                        }
                    }
                }
                else
                {
                    // OLD FORMAT: Process using existing logic
                    var testDetails = JsonConvert.DeserializeObject<DraftTestDetail>(pushWorkSheetDetail.TestJson);
                    
                    if (testDetails != null)
                    {
                        var WorksheetModel = new WorkSheetModel
                        {
                            TestName = testDetails.TestName,
                            Procedure = testDetails.Procedure,
                            DocumentNo = pushWorkSheetDetail.DocumentNo,
                            ISMOA = pushWorkSheetDetail.ISMOA == 1
                        };

                        await TrnCldFinalSubmission(plantId, 1, 0, 0, 0, 1, 1, 1, 
                                                  Guid.NewGuid().ToString(), WorksheetModel);
                    }
                }

                // Mark as processed
                pushWorkSheetDetail.Status = 1;
            }
            catch (Exception ex)
            {
                // Log error but continue processing other records
                pushWorkSheetDetail.Status = -1; // Error status
            }
        }

        await _dbContext.SaveChangesAsync();
        return Ok("Worksheet has been parsed and Successfully Submitted");
    }
    catch (Exception ex)
    {
        return StatusCode(500, "An error occurred while parsing worksheet JSON.");
    }
}
```

**Key Features:**
- **Dual Format Support**: Handles both old and new JSON formats
- **WSTypes Processing**: Creates separate worksheets for each section
- **Backward Compatibility**: Existing JSON format continues to work
- **Error Handling**: Continues processing even if individual records fail

---

## 🗄️ Database Changes

### 1. **Worksheet Processing Status**
**Table**: `LMS_BAL_TST_DET_INF_WST` (Status Management)

```sql
-- Status values for worksheet processing
-- 0 = Unprocessed (ready for worksheet generation)
-- 1 = Processed successfully
-- -1 = Processing error

-- Query to check processing status
SELECT 
    DocumentNo,
    Status,
    CASE 
        WHEN Status = 0 THEN 'Pending Processing'
        WHEN Status = 1 THEN 'Processed Successfully'
        WHEN Status = -1 THEN 'Processing Error'
    END AS StatusDescription,
    CreatedOn,
    PublishedDate
FROM LMS_BAL_TST_DET_INF_WST
ORDER BY CreatedOn DESC;
```

### 2. **Worksheet Creation Process**
**Stored Procedure**: `TrnCldFinalSubmission`

```csharp
// ENHANCED: Worksheet creation with improved processing
[HttpPost]
[Route("TrnCldFinalSubmission")]
public async Task<IActionResult> TrnCldFinalSubmission(int plantId, int IsSaved, int baseId, 
    int aulId, int transId, int transType, int userId, int configId, string transCode, 
    [FromBody] WorkSheetModel workSheetModel)
{
    try
    {
        // Initialize WorkSheetService with plant-specific configuration
        using var workSheetService = new WorkSheetService(PlanInfoList.GetPlanInfo(plantId).PlantCode)
        {
            IsSaved = IsSaved,
            BaseId = baseId,
            AulId = aulId,
            TransId = transId,
            TransType = transType,
            UserId = userId,
            ConfigId = configId,
            TransCode = transCode
        };

        // Process worksheet data
        var result = await workSheetService.ProcessWorksheet(workSheetModel);
        
        return Ok(new CommonReturnSummery 
        { 
            ReturnStatus = result.ReturnStatus, 
            MsgNo = result.MsgNo, 
            KeyField = result.KeyField 
        });
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error processing worksheet submission.");
    }
}
```

### 3. **MOA Worksheet Support**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

```csharp
// NEW: Separate endpoint for MOA worksheets
[HttpPost]
[Route("MOAWorksheetPushTestDetails")]
public async Task<IActionResult> MOAWorksheetPushTestDetails([FromBody] PushWSDetailsDto PushWSDetailsDto)
{
    if (PushWSDetailsDto == null || string.IsNullOrEmpty(PushWSDetailsDto.TestJson))
    {
        return BadRequest("Invalid request data.");
    }
    
    try
    {
        // Check for existing MOA records
        var latestRecord = await _dbContext.LMS_BAL_TST_DET_INF_WST
              .Where(x => x.DocumentNo == PushWSDetailsDto.DocumentNo
                && x.JsonId == PushWSDetailsDto.JsonId)
               .OrderByDescending(x => x.PublishedDate)
               .FirstOrDefaultAsync();

        if (latestRecord == null || PushWSDetailsDto.PublishedDate > latestRecord.PublishedDate)
        {
            PushWorkSheetDetails newRecord = new PushWorkSheetDetails
            {
                DocumentNo = PushWSDetailsDto.DocumentNo,
                CreatedOn = PushWSDetailsDto.CreatedOn,
                TestJson = PushWSDetailsDto.TestJson,
                Status = 0,
                ISMOA = 1, // MOA flag
                PublishedDate = PushWSDetailsDto.PublishedDate,
                JsonId = PushWSDetailsDto.JsonId
            };

            await _dbContext.LMS_BAL_TST_DET_INF_WST.AddAsync(newRecord);
            await _dbContext.SaveChangesAsync();
        }
        
        return Ok("MOA worksheet details stored successfully.");
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error storing MOA worksheet details.");
    }
}
```

### 4. **Last Published Date Tracking**
```csharp
// NEW: Track last published date for synchronization
[HttpGet]
[Route("ReadWSLastPublishedDT")]
public async Task<IActionResult> ReadWSLastPublishedDT(bool IsMoa)
{
    try
    {
        var latestRecord = _dbContext.LMS_BAL_TST_DET_INF_WST
             .Where(x => x.ISMOA == (IsMoa ? 1 : 0))
             .OrderByDescending(x => x.PublishedDate)
             .FirstOrDefault();

        return Ok(latestRecord?.PublishedDate);
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error retrieving last published date.");
    }
}
```

---

## 🔄 Data Flow

```
1. WorksheetJSONParsing API triggered (manual or scheduled)
   ↓
2. Query LMS_BAL_TST_DET_INF_WST for Status = 0 records
   ↓
3. For each unprocessed record:
   ↓
4. Try parsing as WSTypes format first
   ↓
5a. IF WSTypes found:
    → Create separate worksheet for each section
    → Use SecName as TestName, Procedure as content
   ↓
5b. IF WSTypes NOT found:
    → Use old format parsing
    → Create single worksheet with TestName and Procedure
   ↓
6. Call TrnCldFinalSubmission for each worksheet
   ↓
7. Update record Status to 1 (processed)
   ↓
8. Save changes to database
```

---

## 📊 JSON Format Support

### **New Format (WSTypes)**
```json
{
  "Tests": [
    {
      "TestName": "4 )(Q)(S) Dissolution",
      "WSTypes": [
        {
          "Type": "section",
          "SecName": "Buffer preparation for mobile phase",
          "Procedure": "<p>Weigh accurately {[{LIMS_CHQTY}]} g...</p>"
        },
        {
          "Type": "section", 
          "SecName": "Mobile phase preparation",
          "Procedure": "<p>Mix buffer solution with...</p>"
        }
      ],
      "Images": []
    }
  ]
}
```

### **Old Format (Legacy)**
```json
{
  "Tests": [
    {
      "TestName": "Dissolution Test",
      "Procedure": "<p>Complete test procedure...</p>",
      "Images": []
    }
  ]
}
```

---

## 🧪 Testing

### **JSON Processing Testing**
- [ ] WSTypes format creates multiple worksheets
- [ ] Old format creates single worksheet
- [ ] Section names become worksheet test names
- [ ] Procedures are correctly assigned
- [ ] Status updates from 0 to 1 after processing

### **Database Testing**
- [ ] Unprocessed records (Status = 0) are retrieved
- [ ] Records marked as processed (Status = 1) after completion
- [ ] Error records marked with Status = -1
- [ ] MOA worksheets handled separately (ISMOA = 1)

### **Worksheet Creation Testing**
- [ ] TrnCldFinalSubmission creates actual worksheets
- [ ] Plant-specific configuration applied correctly
- [ ] Worksheet data stored in LIMS tables
- [ ] Template IDs and sequence numbers generated

---

## 📋 Files Modified

| File | Purpose | Change |
|------|---------|--------|
| `WorkSheetModel.cs` | Models | Added WSTypes support models |
| `WorkSheetController.cs` | API Controller | Enhanced JSON parsing logic |
| `ApplicationDbContext.cs` | DB Context | Status management queries |

---

## ⚙️ Configuration

### **Processing Schedule**
- Manual trigger via API call
- Can be scheduled for automatic processing
- Configurable batch size for large datasets

### **Error Handling**
- Individual record failures don't stop batch processing
- Error status (-1) for failed records
- Detailed logging for troubleshooting

---

**✅ Worksheet Generation Complete!**
Test data is now processed into actual LIMS worksheets with full WSTypes support.
