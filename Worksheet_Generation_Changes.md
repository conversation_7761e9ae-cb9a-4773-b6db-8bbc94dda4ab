# Worksheet Generation - Developer Guide

## 🎯 Overview
The **Worksheet Generation** system processes stored test data and creates actual worksheets in the LIMS system, supporting both old and new JSON formats with WSTypes.

---

## 🖥️ Application Changes

### 1. **Enhanced JSON Processing Models**
**File**: `Src\Models\CaliberLIMS.Models\WorkSheetModel.cs`

```csharp
// NEW: Model for handling WSTypes JSON format
public class DraftTestDetailWithWSTypes
{
    [JsonProperty("TestName")]
    public string TestName { get; set; }
    
    [JsonProperty("WSTypes")]
    public List<WSType> WSTypes { get; set; }
    
    [JsonProperty("Images")]
    public List<object> Images { get; set; }
}

// NEW: Model for individual worksheet sections
public class WSType
{
    [JsonProperty("Type")]
    public string Type { get; set; }
    
    [JsonProperty("SecName")]
    public string SecName { get; set; }
    
    [JsonProperty("Procedure")]
    public string Procedure { get; set; }
}
```

### 2. **Enhanced JSON Parsing API**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

```csharp
// ENHANCED: WorksheetJSONParsing with dual format support
[HttpGet]
[Route("WorksheetJSONParsing")]
public async Task<IActionResult> WorksheetJSONParsing(int plantId = 1)
{
    try
    {
        // Check for unprocessed records
        if (!await _dbContext.LMS_BAL_TST_DET_INF_WST.AnyAsync(ptd => ptd.Status == 0))
        {
            return NotFound("No New records found or all records are already parsed.");
        }

        // Retrieve unprocessed records
        List<PushWorkSheetDetails> pushWorkSheetDetails = await _dbContext.LMS_BAL_TST_DET_INF_WST
                .Where(ptd => ptd.Status == 0 && ptd.TestJson != null)
                .ToListAsync();

        foreach (var pushWorkSheetDetail in pushWorkSheetDetails)
        {
            try
            {
                // NEW: Try parsing with WSTypes format first
                var testDetailsWithWSTypes = JsonConvert.DeserializeObject<DraftTestDetailWithWSTypes>(pushWorkSheetDetail.TestJson);
                
                if (testDetailsWithWSTypes?.WSTypes != null && testDetailsWithWSTypes.WSTypes.Any())
                {
                    // NEW FORMAT: Process each WSType as separate worksheet
                    foreach (var wsType in testDetailsWithWSTypes.WSTypes)
                    {
                        if (wsType.Type == "section")
                        {
                            var WorksheetModel = new WorkSheetModel
                            {
                                TestName = wsType.SecName,  // Use section name as test name
                                Procedure = wsType.Procedure,
                                DocumentNo = pushWorkSheetDetail.DocumentNo,
                                ISMOA = pushWorkSheetDetail.ISMOA == 1
                            };

                            // Create worksheet for this section
                            await TrnCldFinalSubmission(plantId, 1, 0, 0, 0, 1, 1, 1, 
                                                      Guid.NewGuid().ToString(), WorksheetModel);
                        }
                    }
                }
                else
                {
                    // OLD FORMAT: Process using existing logic
                    var testDetails = JsonConvert.DeserializeObject<DraftTestDetail>(pushWorkSheetDetail.TestJson);
                    
                    if (testDetails != null)
                    {
                        var WorksheetModel = new WorkSheetModel
                        {
                            TestName = testDetails.TestName,
                            Procedure = testDetails.Procedure,
                            DocumentNo = pushWorkSheetDetail.DocumentNo,
                            ISMOA = pushWorkSheetDetail.ISMOA == 1
                        };

                        await TrnCldFinalSubmission(plantId, 1, 0, 0, 0, 1, 1, 1, 
                                                  Guid.NewGuid().ToString(), WorksheetModel);
                    }
                }

                // Mark as processed
                pushWorkSheetDetail.Status = 1;
            }
            catch (Exception ex)
            {
                // Log error but continue processing other records
                pushWorkSheetDetail.Status = -1; // Error status
            }
        }

        await _dbContext.SaveChangesAsync();
        return Ok("Worksheet has been parsed and Successfully Submitted");
    }
    catch (Exception ex)
    {
        return StatusCode(500, "An error occurred while parsing worksheet JSON.");
    }
}
```

**Key Features:**
- **Dual Format Support**: Handles both old and new JSON formats
- **WSTypes Processing**: Creates separate worksheets for each section
- **Backward Compatibility**: Existing JSON format continues to work
- **Error Handling**: Continues processing even if individual records fail

---

## 🗄️ Database Changes

### 1. **Missing Test Table and Models**

#### LMS_BAL_TST_DET_INF Table (MISSING - NEEDS TO BE ADDED)
**Purpose**: Stores test details from external sources for processing

**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\ApplicationDbContext.cs`

<augment_code_snippet path="Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\ApplicationDbContext.cs" mode="EXCERPT">
````csharp
/// <summary>
/// Represents the table in the database for storing push test details.
/// Maps to the LMS_BAL_TST_DET_INF table in the database.
/// </summary>
public DbSet<PushTestDetails> LMS_BAL_TST_DET_INF { get; set; }
````
</augment_code_snippet>

#### Missing Models (NEED TO BE ADDED)
**File**: `Src\Models\CaliberLIMS.Models\WorkSheetModel.cs`

```csharp
// MISSING: Entity for test data storage in LMS_BAL_TST_DET_INF table
public class PushTestDetails
{
    [Column("LMS_BAL_TST_DET_INF_ID")]
    public int Id { get; set; }

    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
    public int Status { get; set; }
    public string PublishedDate { get; set; }
    public string JsonId { get; set; }
}

// MISSING: DTO for test API operations
public class PushTestRequest
{
    public string DocumentNo { get; set; }
    public DateTime CreatedOn { get; set; }
    public string TestJson { get; set; }
}
```

#### Missing API Endpoint (NEEDS TO BE ADDED)
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

```csharp
// MISSING: Endpoint to store test data from external API
[HttpPost]
[Route("PushTestDetails")]
public async Task<IActionResult> PushTestDetails([FromBody] PushTestRequest pushTestRequest)
{
    if (pushTestRequest == null || string.IsNullOrEmpty(pushTestRequest.TestJson))
    {
        return BadRequest("Invalid request data.");
    }

    try
    {
        // Check for existing records (duplicate prevention)
        var latestRecord = await _dbContext.LMS_BAL_TST_DET_INF
               .Where(x => x.DocumentNo == pushTestRequest.DocumentNo)
               .OrderByDescending(x => x.CreatedOn)
               .FirstOrDefaultAsync();

        // Only insert if no record exists or this is newer
        if (latestRecord == null || pushTestRequest.CreatedOn > latestRecord.CreatedOn)
        {
            PushTestDetails newRecord = new PushTestDetails
            {
                DocumentNo = pushTestRequest.DocumentNo,
                CreatedOn = pushTestRequest.CreatedOn,
                TestJson = pushTestRequest.TestJson,
                Status = 0, // Unprocessed
                PublishedDate = pushTestRequest.CreatedOn.ToString("yyyy-MM-dd HH:mm:ss"),
                JsonId = Guid.NewGuid().ToString()
            };

            await _dbContext.LMS_BAL_TST_DET_INF.AddAsync(newRecord);
            await _dbContext.SaveChangesAsync();
        }

        return Ok("Test details stored successfully.");
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error storing test details.");
    }
}
```

#### Database Schema for Test Table (NEEDS TO BE CREATED)
```sql
-- Create table for test data storage
CREATE TABLE LMS_BAL_TST_DET_INF (
    LMS_BAL_TST_DET_INF_ID INT IDENTITY(1,1) PRIMARY KEY,
    DocumentNo NVARCHAR(255),           -- Document number from external system
    CreatedOn DATETIME,                 -- When record was created
    TestJson NTEXT,                     -- Raw JSON data from external API
    Status INT DEFAULT 0,               -- Processing status (0=unprocessed, 1=processed)
    PublishedDate NVARCHAR(50),         -- Publication date from external system
    JsonId NVARCHAR(255)                -- Unique identifier for JSON data
);

-- Create indexes for performance
CREATE INDEX IX_LMS_BAL_TST_DET_INF_Status ON LMS_BAL_TST_DET_INF (Status);
CREATE INDEX IX_LMS_BAL_TST_DET_INF_DocNo ON LMS_BAL_TST_DET_INF (DocumentNo);
```

### 2. **Worksheet Processing Status**
**Table**: `LMS_BAL_TST_DET_INF_WST` (Status Management)

```sql
-- Status values for worksheet processing
-- 0 = Unprocessed (ready for worksheet generation)
-- 1 = Processed successfully
-- -1 = Processing error

-- Query to check processing status
SELECT 
    DocumentNo,
    Status,
    CASE 
        WHEN Status = 0 THEN 'Pending Processing'
        WHEN Status = 1 THEN 'Processed Successfully'
        WHEN Status = -1 THEN 'Processing Error'
    END AS StatusDescription,
    CreatedOn,
    PublishedDate
FROM LMS_BAL_TST_DET_INF_WST
ORDER BY CreatedOn DESC;
```

### 2. **Worksheet Creation Process**
**Stored Procedure**: `TrnCldFinalSubmission`

```csharp
// ENHANCED: Worksheet creation with improved processing
[HttpPost]
[Route("TrnCldFinalSubmission")]
public async Task<IActionResult> TrnCldFinalSubmission(int plantId, int IsSaved, int baseId, 
    int aulId, int transId, int transType, int userId, int configId, string transCode, 
    [FromBody] WorkSheetModel workSheetModel)
{
    try
    {
        // Initialize WorkSheetService with plant-specific configuration
        using var workSheetService = new WorkSheetService(PlanInfoList.GetPlanInfo(plantId).PlantCode)
        {
            IsSaved = IsSaved,
            BaseId = baseId,
            AulId = aulId,
            TransId = transId,
            TransType = transType,
            UserId = userId,
            ConfigId = configId,
            TransCode = transCode
        };

        // Process worksheet data
        var result = await workSheetService.ProcessWorksheet(workSheetModel);
        
        return Ok(new CommonReturnSummery 
        { 
            ReturnStatus = result.ReturnStatus, 
            MsgNo = result.MsgNo, 
            KeyField = result.KeyField 
        });
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error processing worksheet submission.");
    }
}
```

### 3. **MOA Worksheet Support**
**File**: `Src\Services\CaliberLIMS.Api\CaliberLIMS.Api\MastersModule\Controllers\WorkSheetController.cs`

```csharp
// NEW: Separate endpoint for MOA worksheets
[HttpPost]
[Route("MOAWorksheetPushTestDetails")]
public async Task<IActionResult> MOAWorksheetPushTestDetails([FromBody] PushWSDetailsDto PushWSDetailsDto)
{
    if (PushWSDetailsDto == null || string.IsNullOrEmpty(PushWSDetailsDto.TestJson))
    {
        return BadRequest("Invalid request data.");
    }
    
    try
    {
        // Check for existing MOA records
        var latestRecord = await _dbContext.LMS_BAL_TST_DET_INF_WST
              .Where(x => x.DocumentNo == PushWSDetailsDto.DocumentNo
                && x.JsonId == PushWSDetailsDto.JsonId)
               .OrderByDescending(x => x.PublishedDate)
               .FirstOrDefaultAsync();

        if (latestRecord == null || PushWSDetailsDto.PublishedDate > latestRecord.PublishedDate)
        {
            PushWorkSheetDetails newRecord = new PushWorkSheetDetails
            {
                DocumentNo = PushWSDetailsDto.DocumentNo,
                CreatedOn = PushWSDetailsDto.CreatedOn,
                TestJson = PushWSDetailsDto.TestJson,
                Status = 0,
                ISMOA = 1, // MOA flag
                PublishedDate = PushWSDetailsDto.PublishedDate,
                JsonId = PushWSDetailsDto.JsonId
            };

            await _dbContext.LMS_BAL_TST_DET_INF_WST.AddAsync(newRecord);
            await _dbContext.SaveChangesAsync();
        }
        
        return Ok("MOA worksheet details stored successfully.");
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error storing MOA worksheet details.");
    }
}
```

### 4. **Last Published Date Tracking**
```csharp
// NEW: Track last published date for synchronization
[HttpGet]
[Route("ReadWSLastPublishedDT")]
public async Task<IActionResult> ReadWSLastPublishedDT(bool IsMoa)
{
    try
    {
        var latestRecord = _dbContext.LMS_BAL_TST_DET_INF_WST
             .Where(x => x.ISMOA == (IsMoa ? 1 : 0))
             .OrderByDescending(x => x.PublishedDate)
             .FirstOrDefault();

        return Ok(latestRecord?.PublishedDate);
    }
    catch (Exception ex)
    {
        return StatusCode(500, "Error retrieving last published date.");
    }
}
```

---

## 🔄 Data Flow

```
1. WorksheetJSONParsing API triggered (manual or scheduled)
   ↓
2. Query LMS_BAL_TST_DET_INF_WST for Status = 0 records
   ↓
3. For each unprocessed record:
   ↓
4. Try parsing as WSTypes format first
   ↓
5a. IF WSTypes found:
    → Create separate worksheet for each section
    → Use SecName as TestName, Procedure as content
   ↓
5b. IF WSTypes NOT found:
    → Use old format parsing
    → Create single worksheet with TestName and Procedure
   ↓
6. Call TrnCldFinalSubmission for each worksheet
   ↓
7. Update record Status to 1 (processed)
   ↓
8. Save changes to database
```

---

## 📊 JSON Format Support

### **New Format (WSTypes)**
```json
{
  "Tests": [
    {
      "TestName": "4 )(Q)(S) Dissolution",
      "WSTypes": [
        {
          "Type": "section",
          "SecName": "Buffer preparation for mobile phase",
          "Procedure": "<p>Weigh accurately {[{LIMS_CHQTY}]} g...</p>"
        },
        {
          "Type": "section", 
          "SecName": "Mobile phase preparation",
          "Procedure": "<p>Mix buffer solution with...</p>"
        }
      ],
      "Images": []
    }
  ]
}
```

### **Old Format (Legacy)**
```json
{
  "Tests": [
    {
      "TestName": "Dissolution Test",
      "Procedure": "<p>Complete test procedure...</p>",
      "Images": []
    }
  ]
}
```

---

## 🔧 Advanced Processing Features

### **Template Generation**
```csharp
// Enhanced template ID generation for non-MOA worksheets
private string GenerateTemplateId(string documentNo, string testName)
{
    var timestamp = DateTime.Now.ToString("yyyyMMddHHmmss");
    var hash = testName.GetHashCode().ToString("X");
    return $"TPL_{documentNo}_{hash}_{timestamp}";
}

// Sequence number generation
private int GenerateSequenceNumber(string documentNo)
{
    var existingCount = _dbContext.Worksheets
        .Count(w => w.DocumentNo.StartsWith(documentNo));
    return existingCount + 1;
}
```

### **HTML Token Replacement**
```csharp
// Enhanced procedure HTML processing
private string ProcessProcedureHtml(string procedureHtml, WorkSheetModel model)
{
    if (string.IsNullOrEmpty(procedureHtml))
        return procedureHtml;

    // Replace LIMS tokens with actual values
    var processedHtml = procedureHtml
        .Replace("{[{LIMS_CHQTY}]}", model.ChemicalQuantity?.ToString() ?? "___")
        .Replace("{[{LIMS_EWS}]}", model.EquipmentWashSolution ?? "___")
        .Replace("{[{LIMS_TEMP}]}", model.Temperature?.ToString() ?? "___")
        .Replace("{[{LIMS_TIME}]}", model.Time?.ToString() ?? "___");

    // Add timestamp and user information
    processedHtml += $"<br/><small>Generated on: {DateTime.Now:yyyy-MM-dd HH:mm:ss}</small>";

    return processedHtml;
}
```

### **Batch Processing Optimization**
```csharp
// Enhanced batch processing with parallel execution
[HttpGet]
[Route("WorksheetJSONParsingBatch")]
public async Task<IActionResult> WorksheetJSONParsingBatch(int plantId = 1, int batchSize = 50)
{
    try
    {
        var totalProcessed = 0;
        var totalErrors = 0;

        while (true)
        {
            // Get batch of unprocessed records
            var batch = await _dbContext.LMS_BAL_TST_DET_INF_WST
                .Where(ptd => ptd.Status == 0 && ptd.TestJson != null)
                .Take(batchSize)
                .ToListAsync();

            if (!batch.Any())
                break;

            // Process batch in parallel
            var tasks = batch.Select(async record =>
            {
                try
                {
                    await ProcessSingleRecord(record, plantId);
                    record.Status = 1;
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing record {Id}", record.Id);
                    record.Status = -1;
                    return false;
                }
            });

            var results = await Task.WhenAll(tasks);
            totalProcessed += results.Count(r => r);
            totalErrors += results.Count(r => !r);

            await _dbContext.SaveChangesAsync();
        }

        return Ok(new { TotalProcessed = totalProcessed, TotalErrors = totalErrors });
    }
    catch (Exception ex)
    {
        return StatusCode(500, $"Batch processing error: {ex.Message}");
    }
}
```

---

## 🗄️ Advanced Database Operations

### **Stored Procedures for Worksheet Processing**
```sql
-- Create stored procedure for worksheet finalization
CREATE PROCEDURE SP_FinalizeWorksheet
    @PlantId INT,
    @DocumentNo NVARCHAR(255),
    @TestName NVARCHAR(500),
    @Procedure NTEXT,
    @IsMOA BIT,
    @UserId INT,
    @WorksheetId INT OUTPUT
AS
BEGIN
    SET NOCOUNT ON;

    BEGIN TRANSACTION;

    TRY
        -- Insert main worksheet record
        INSERT INTO LMS_WORKSHEETS (
            PlantId, DocumentNo, TestName, Procedure,
            IsMOA, CreatedBy, CreatedOn, Status
        )
        VALUES (
            @PlantId, @DocumentNo, @TestName, @Procedure,
            @IsMOA, @UserId, GETDATE(), 'Active'
        );

        SET @WorksheetId = SCOPE_IDENTITY();

        -- Generate template if not MOA
        IF @IsMOA = 0
        BEGIN
            INSERT INTO LMS_WORKSHEET_TEMPLATES (
                WorksheetId, TemplateId, SequenceNo, GeneratedOn
            )
            VALUES (
                @WorksheetId,
                'TPL_' + @DocumentNo + '_' + CAST(@WorksheetId AS NVARCHAR),
                (SELECT ISNULL(MAX(SequenceNo), 0) + 1 FROM LMS_WORKSHEET_TEMPLATES WHERE WorksheetId = @WorksheetId),
                GETDATE()
            );
        END

        COMMIT TRANSACTION;
    END TRY
    BEGIN CATCH
        ROLLBACK TRANSACTION;
        THROW;
    END CATCH
END
```

### **Data Archiving Strategy**
```sql
-- Create archive table
CREATE TABLE LMS_BAL_TST_DET_INF_WST_ARCHIVE (
    LMS_BAL_TST_DET_INF_ID INT,
    DocumentNo NVARCHAR(255),
    CreatedOn DATETIME,
    TestJson NTEXT,
    Status INT,
    ISMOA INT,
    PublishedDate NVARCHAR(50),
    JsonId NVARCHAR(255),
    ArchivedOn DATETIME DEFAULT GETDATE()
);

-- Archive processed records older than 6 months
CREATE PROCEDURE SP_ArchiveProcessedWorksheets
AS
BEGIN
    INSERT INTO LMS_BAL_TST_DET_INF_WST_ARCHIVE
    SELECT *, GETDATE()
    FROM LMS_BAL_TST_DET_INF_WST
    WHERE Status = 1
    AND CreatedOn < DATEADD(MONTH, -6, GETDATE());

    DELETE FROM LMS_BAL_TST_DET_INF_WST
    WHERE Status = 1
    AND CreatedOn < DATEADD(MONTH, -6, GETDATE());
END
```

### **Performance Monitoring Views**
```sql
-- Create view for processing statistics
CREATE VIEW VW_WorksheetProcessingStats AS
SELECT
    CAST(CreatedOn AS DATE) as ProcessingDate,
    COUNT(*) as TotalRecords,
    SUM(CASE WHEN Status = 0 THEN 1 ELSE 0 END) as Pending,
    SUM(CASE WHEN Status = 1 THEN 1 ELSE 0 END) as Processed,
    SUM(CASE WHEN Status = -1 THEN 1 ELSE 0 END) as Errors,
    SUM(CASE WHEN ISMOA = 1 THEN 1 ELSE 0 END) as MOARecords,
    AVG(CASE WHEN Status = 1 THEN
        DATEDIFF(MINUTE, CreatedOn, GETDATE())
        ELSE NULL END) as AvgProcessingTimeMinutes
FROM LMS_BAL_TST_DET_INF_WST
WHERE CreatedOn >= DATEADD(DAY, -30, GETDATE())
GROUP BY CAST(CreatedOn AS DATE);
```

---

## 🔄 Error Recovery & Retry Logic

### **Automatic Retry Mechanism**
```csharp
// Enhanced error handling with retry logic
private async Task<bool> ProcessSingleRecordWithRetry(PushWorkSheetDetails record, int plantId, int maxRetries = 3)
{
    for (int attempt = 1; attempt <= maxRetries; attempt++)
    {
        try
        {
            await ProcessSingleRecord(record, plantId);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Attempt {Attempt} failed for record {Id}", attempt, record.Id);

            if (attempt == maxRetries)
            {
                _logger.LogError(ex, "All {MaxRetries} attempts failed for record {Id}", maxRetries, record.Id);
                return false;
            }

            // Exponential backoff
            await Task.Delay(TimeSpan.FromSeconds(Math.Pow(2, attempt)));
        }
    }

    return false;
}
```

### **Dead Letter Queue for Failed Records**
```csharp
// Move failed records to dead letter queue
[HttpPost]
[Route("ProcessDeadLetterQueue")]
public async Task<IActionResult> ProcessDeadLetterQueue(int plantId = 1)
{
    try
    {
        var failedRecords = await _dbContext.LMS_BAL_TST_DET_INF_WST
            .Where(r => r.Status == -1)
            .ToListAsync();

        var reprocessedCount = 0;

        foreach (var record in failedRecords)
        {
            // Reset status for retry
            record.Status = 0;

            if (await ProcessSingleRecordWithRetry(record, plantId))
            {
                record.Status = 1;
                reprocessedCount++;
            }
            else
            {
                record.Status = -2; // Permanently failed
            }
        }

        await _dbContext.SaveChangesAsync();

        return Ok(new {
            TotalFailed = failedRecords.Count,
            Reprocessed = reprocessedCount,
            PermanentlyFailed = failedRecords.Count - reprocessedCount
        });
    }
    catch (Exception ex)
    {
        return StatusCode(500, $"Dead letter queue processing error: {ex.Message}");
    }
}
```

---

## 🚀 Deployment & Configuration

### **Scheduled Processing Setup**
```csharp
// Add background service for automatic processing
public class WorksheetProcessingService : BackgroundService
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<WorksheetProcessingService> _logger;
    private readonly IConfiguration _configuration;

    protected override async Task ExecuteAsync(CancellationToken stoppingToken)
    {
        var intervalMinutes = _configuration.GetValue<int>("WorksheetProcessing:IntervalMinutes", 5);

        while (!stoppingToken.IsCancellationRequested)
        {
            try
            {
                using var scope = _serviceProvider.CreateScope();
                var controller = scope.ServiceProvider.GetRequiredService<WorkSheetController>();

                await controller.WorksheetJSONParsing(1);
                _logger.LogInformation("Worksheet processing completed at {Time}", DateTime.Now);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in scheduled worksheet processing");
            }

            await Task.Delay(TimeSpan.FromMinutes(intervalMinutes), stoppingToken);
        }
    }
}

// Register in Program.cs
services.AddHostedService<WorksheetProcessingService>();
```

### **Configuration Settings**
```json
// appsettings.json
{
  "WorksheetProcessing": {
    "IntervalMinutes": 5,
    "BatchSize": 50,
    "MaxRetries": 3,
    "EnableAutoProcessing": true,
    "ArchiveAfterDays": 180
  },
  "Logging": {
    "LogLevel": {
      "CaliberLIMS.Api.MastersModule.Controllers.WorkSheetController": "Information"
    }
  }
}
```

---

## 📊 Monitoring & Analytics

### **Processing Dashboard Queries**
```sql
-- Real-time processing status
SELECT
    'Pending' as Status, COUNT(*) as Count
FROM LMS_BAL_TST_DET_INF_WST WHERE Status = 0
UNION ALL
SELECT
    'Processed' as Status, COUNT(*) as Count
FROM LMS_BAL_TST_DET_INF_WST WHERE Status = 1
UNION ALL
SELECT
    'Failed' as Status, COUNT(*) as Count
FROM LMS_BAL_TST_DET_INF_WST WHERE Status = -1;

-- Processing performance metrics
SELECT
    AVG(DATEDIFF(MINUTE, CreatedOn,
        CASE WHEN Status = 1 THEN GETDATE() ELSE NULL END)) as AvgProcessingTimeMinutes,
    COUNT(CASE WHEN Status = 1 THEN 1 END) as ProcessedToday,
    COUNT(CASE WHEN Status = -1 THEN 1 END) as FailedToday
FROM LMS_BAL_TST_DET_INF_WST
WHERE CAST(CreatedOn AS DATE) = CAST(GETDATE() AS DATE);
```

### **Health Check Implementation**
```csharp
// Worksheet processing health check
public class WorksheetProcessingHealthCheck : IHealthCheck
{
    private readonly ApplicationDbContext _dbContext;

    public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context,
        CancellationToken cancellationToken = default)
    {
        try
        {
            // Check for stuck records (pending > 1 hour)
            var stuckCount = await _dbContext.LMS_BAL_TST_DET_INF_WST
                .CountAsync(r => r.Status == 0 && r.CreatedOn < DateTime.Now.AddHours(-1), cancellationToken);

            if (stuckCount > 10)
            {
                return HealthCheckResult.Unhealthy($"{stuckCount} records stuck in processing");
            }

            // Check error rate
            var totalToday = await _dbContext.LMS_BAL_TST_DET_INF_WST
                .CountAsync(r => r.CreatedOn >= DateTime.Today, cancellationToken);

            var errorsToday = await _dbContext.LMS_BAL_TST_DET_INF_WST
                .CountAsync(r => r.Status == -1 && r.CreatedOn >= DateTime.Today, cancellationToken);

            var errorRate = totalToday > 0 ? (double)errorsToday / totalToday : 0;

            if (errorRate > 0.1) // 10% error rate threshold
            {
                return HealthCheckResult.Degraded($"High error rate: {errorRate:P}");
            }

            return HealthCheckResult.Healthy("Worksheet processing is healthy");
        }
        catch (Exception ex)
        {
            return HealthCheckResult.Unhealthy($"Health check failed: {ex.Message}");
        }
    }
}
```

---

## 🔧 Maintenance & Optimization

### **Database Maintenance Scripts**
```sql
-- Clean up old processed records
CREATE PROCEDURE SP_CleanupOldWorksheetData
    @RetentionDays INT = 180
AS
BEGIN
    DECLARE @CutoffDate DATETIME = DATEADD(DAY, -@RetentionDays, GETDATE());

    -- Archive old records
    INSERT INTO LMS_BAL_TST_DET_INF_WST_ARCHIVE
    SELECT *, GETDATE()
    FROM LMS_BAL_TST_DET_INF_WST
    WHERE Status = 1 AND CreatedOn < @CutoffDate;

    -- Delete archived records
    DELETE FROM LMS_BAL_TST_DET_INF_WST
    WHERE Status = 1 AND CreatedOn < @CutoffDate;

    -- Update statistics
    UPDATE STATISTICS LMS_BAL_TST_DET_INF_WST;
END

-- Schedule cleanup job
EXEC sp_add_job
    @job_name = 'Cleanup Old Worksheet Data',
    @enabled = 1;

EXEC sp_add_jobstep
    @job_name = 'Cleanup Old Worksheet Data',
    @step_name = 'Run Cleanup',
    @command = 'EXEC SP_CleanupOldWorksheetData @RetentionDays = 180';

EXEC sp_add_schedule
    @schedule_name = 'Weekly Cleanup',
    @freq_type = 4, -- Weekly
    @freq_interval = 1; -- Sunday
```

### **Performance Optimization**
```csharp
// Optimized bulk processing
[HttpPost]
[Route("BulkWorksheetProcessing")]
public async Task<IActionResult> BulkWorksheetProcessing(int plantId = 1, int batchSize = 100)
{
    try
    {
        var processedCount = 0;

        // Use raw SQL for better performance on large datasets
        var sql = @"
            SELECT TOP (@batchSize) *
            FROM LMS_BAL_TST_DET_INF_WST
            WHERE Status = 0 AND TestJson IS NOT NULL
            ORDER BY CreatedOn";

        while (true)
        {
            var batch = await _dbContext.LMS_BAL_TST_DET_INF_WST
                .FromSqlRaw(sql, new SqlParameter("@batchSize", batchSize))
                .AsNoTracking()
                .ToListAsync();

            if (!batch.Any())
                break;

            // Process in parallel with controlled concurrency
            var semaphore = new SemaphoreSlim(Environment.ProcessorCount);
            var tasks = batch.Select(async record =>
            {
                await semaphore.WaitAsync();
                try
                {
                    return await ProcessSingleRecord(record, plantId);
                }
                finally
                {
                    semaphore.Release();
                }
            });

            var results = await Task.WhenAll(tasks);

            // Bulk update status
            var successIds = batch.Where((r, i) => results[i]).Select(r => r.Id).ToList();
            var failedIds = batch.Where((r, i) => !results[i]).Select(r => r.Id).ToList();

            if (successIds.Any())
            {
                await _dbContext.Database.ExecuteSqlRawAsync(
                    "UPDATE LMS_BAL_TST_DET_INF_WST SET Status = 1 WHERE LMS_BAL_TST_DET_INF_ID IN ({0})",
                    string.Join(",", successIds));
            }

            if (failedIds.Any())
            {
                await _dbContext.Database.ExecuteSqlRawAsync(
                    "UPDATE LMS_BAL_TST_DET_INF_WST SET Status = -1 WHERE LMS_BAL_TST_DET_INF_ID IN ({0})",
                    string.Join(",", failedIds));
            }

            processedCount += batch.Count;
        }

        return Ok(new { ProcessedCount = processedCount });
    }
    catch (Exception ex)
    {
        return StatusCode(500, $"Bulk processing error: {ex.Message}");
    }
}
```

---

## 📋 Files Modified

| File | Purpose | Change |
|------|---------|--------|
| `WorkSheetModel.cs` | Models | Added WSTypes support models |
| `WorkSheetController.cs` | API Controller | Enhanced JSON parsing with dual format support |
| `ApplicationDbContext.cs` | DB Context | Status management and queries |
| `Program.cs` | Startup | Added background service registration |
| `appsettings.json` | Configuration | Added worksheet processing settings |

---

## 📞 Support & Troubleshooting

### **Common Issues & Solutions**

**Issue**: Records stuck in Status = 0
- **Solution**: Run dead letter queue processing or restart background service

**Issue**: High memory usage during processing
- **Solution**: Reduce batch size in configuration

**Issue**: JSON parsing errors
- **Solution**: Validate JSON structure and add error logging

**Issue**: Worksheet creation failures
- **Solution**: Check TrnCldFinalSubmission parameters and plant configuration

### **Monitoring Alerts**
```sql
-- Alert for high error rate
IF (SELECT COUNT(*) FROM LMS_BAL_TST_DET_INF_WST WHERE Status = -1 AND CreatedOn >= DATEADD(HOUR, -1, GETDATE())) > 10
BEGIN
    EXEC msdb.dbo.sp_send_dbmail
        @profile_name = 'LIMS Alerts',
        @recipients = '<EMAIL>',
        @subject = 'High Worksheet Processing Error Rate',
        @body = 'More than 10 worksheet processing errors in the last hour';
END
```

---

**✅ Worksheet Generation Complete!**
Test data is now processed into actual LIMS worksheets with full WSTypes support, monitoring, and optimization.
